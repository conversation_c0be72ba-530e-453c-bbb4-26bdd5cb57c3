$(document).ready(function() {
    // Initialize DataTable
    var table = $('#sw-datatable').DataTable({
        "processing": true,
        "serverSide": true,
        "ajax": {
            "url": "./sw-mod/patroli/sw-datatable.php",
            "type": "POST",
            "error": function(xhr, error, thrown) {
                console.log('DataTable Error:', error);
                alert('Error loading data. Please check console for details.');
            }
        },
        "columns": [
            { "data": 0, "orderable": false, "searchable": false }, // No
            { "data": 1, "type": "date" }, // Tanggal
            { "data": 2 }, // <PERSON><PERSON><PERSON>
            { "data": 3 }, // Lokasi
            { "data": 4 }, // Status
            { "data": 5, "orderable": false }, // Rating
            { "data": 6, "orderable": false, "searchable": false }, // Dokumentasi
            { "data": 7 }, // Komentar
            { "data": 8, "orderable": false, "searchable": false } // Aksi
        ],
        "order": [[1, "desc"]], // Sort by tanggal descending
        "iDisplayLength": 25,
        "aLengthMenu": [[10, 25, 50, 100, -1], [10, 25, 50, 100, "Semua"]],
        "language": {
            "search": "Cari:",
            "lengthMenu": "Tampilkan _MENU_ data per halaman",
            "zeroRecords": "Data tidak ditemukan",
            "info": "Menampilkan _START_ sampai _END_ dari _TOTAL_ data",
            "infoEmpty": "Menampilkan 0 sampai 0 dari 0 data",
            "infoFiltered": "(disaring dari _MAX_ total data)",
            "paginate": {
                "first": "Pertama",
                "last": "Terakhir",
                "next": "Selanjutnya",
                "previous": "Sebelumnya"
            },
            "processing": "Memproses...",
            "loadingRecords": "Memuat data...",
            "emptyTable": "Tidak ada data patroli"
        }
    });

    // Load dropdown data when modal opens
    $('#modal-add-patroli, #modal-edit-patroli').on('show.bs.modal', function() {
        loadKaryawanOptions();
        loadLokasiOptions();
    });

    // Function to load karyawan options
    function loadKaryawanOptions() {
        $.ajax({
            url: './sw-mod/patroli/proses.php?action=get_karyawan',
            type: 'GET',
            dataType: 'json',
            success: function(data) {
                var options = '<option value="">-- Pilih Karyawan --</option>';
                $.each(data, function(index, item) {
                    options += '<option value="' + item.id + '">' + item.employees_code + ' - ' + item.employees_name + '</option>';
                });
                $('select[name="id_karyawan"]').html(options);
            },
            error: function() {
                console.log('Error loading karyawan data');
            }
        });
    }

    // Function to load lokasi options
    function loadLokasiOptions() {
        $.ajax({
            url: './sw-mod/patroli/proses.php?action=get_lokasi',
            type: 'GET',
            dataType: 'json',
            success: function(data) {
                var options = '<option value="">-- Pilih Lokasi --</option>';
                $.each(data, function(index, item) {
                    options += '<option value="' + item.building_id + '">' + item.building_name + '</option>';
                });
                $('select[name="id_lokasi"]').html(options);
            },
            error: function() {
                console.log('Error loading lokasi data');
            }
        });
    }

    // Function for loading animation
    function loading() {
        $(".loading").show();
        $(".loading").delay(1500).fadeOut(500);
    }

    // Add Patroli Form Submit
    $('.add-patroli').submit(function(e) {
        e.preventDefault();
        
        // Validation
        if ($('select[name="id_karyawan"]').val() == '' || 
            $('select[name="id_lokasi"]').val() == '' || 
            $('input[name="tanggal"]').val() == '' || 
            $('select[name="status"]').val() == '') {
            swal({
                title: 'Oops!', 
                text: 'Harap lengkapi semua field yang wajib diisi!', 
                icon: 'error', 
                timer: 1500
            });
            return false;
        }

        loading();
        $.ajax({
            url: "./sw-mod/patroli/proses.php?action=add",
            type: "POST",
            data: new FormData(this),
            processData: false,
            contentType: false,
            cache: false,
            async: false,
            beforeSend: function() { 
                loading();
            },
            success: function(data) {
                if (data == 'success') {
                    swal({
                        title: 'Berhasil!', 
                        text: 'Data patroli berhasil ditambahkan!', 
                        icon: 'success', 
                        timer: 1500
                    });
                    $('#modal-add-patroli').modal('hide');
                    $('.add-patroli')[0].reset();
                    $('#sw-datatable').DataTable().ajax.reload();
                } else {
                    swal({
                        title: 'Oops!', 
                        text: data, 
                        icon: 'error', 
                        timer: 2000
                    });
                }
            },
            complete: function() {
                $(".loading").hide();
            },
            error: function() {
                swal({
                    title: 'Error!', 
                    text: 'Terjadi kesalahan sistem!', 
                    icon: 'error', 
                    timer: 1500
                });
            }
        });
    });

    // Edit Patroli Form Submit
    $('.edit-patroli').submit(function(e) {
        e.preventDefault();
        
        loading();
        $.ajax({
            url: "./sw-mod/patroli/proses.php?action=edit",
            type: "POST",
            data: new FormData(this),
            processData: false,
            contentType: false,
            cache: false,
            async: false,
            beforeSend: function() { 
                loading();
            },
            success: function(data) {
                if (data == 'success') {
                    swal({
                        title: 'Berhasil!', 
                        text: 'Data patroli berhasil diupdate!', 
                        icon: 'success', 
                        timer: 1500
                    });
                    $('#modal-edit-patroli').modal('hide');
                    $('#sw-datatable').DataTable().ajax.reload();
                } else {
                    swal({
                        title: 'Oops!', 
                        text: data, 
                        icon: 'error', 
                        timer: 2000
                    });
                }
            },
            complete: function() {
                $(".loading").hide();
            },
            error: function() {
                swal({
                    title: 'Error!', 
                    text: 'Terjadi kesalahan sistem!', 
                    icon: 'error', 
                    timer: 1500
                });
            }
        });
    });
});

// Function to edit patroli
function editPatroli(id) {
    $.ajax({
        url: './sw-mod/patroli/proses.php?action=get_detail&id=' + id,
        type: 'GET',
        dataType: 'json',
        success: function(data) {
            if (data.status == 'success') {
                var patroli = data.data;
                
                // Fill form fields
                $('#edit-id-patroli').val(patroli.id_patroli);
                $('#edit-id-karyawan').val(patroli.id_karyawan);
                $('#edit-id-lokasi').val(patroli.id_lokasi);
                $('#edit-tanggal').val(patroli.tanggal);
                $('#edit-status').val(patroli.status);
                $('#edit-rating').val(patroli.rating);
                $('#edit-id-ceklis').val(patroli.id_ceklis);
                $('#edit-komentar').val(patroli.komentar);
                
                // Show current documentation if exists
                if (patroli.dokumentasi) {
                    var fileExt = patroli.dokumentasi.split('.').pop().toLowerCase();
                    var currentDoc = '';
                    
                    if (['jpg', 'jpeg', 'png', 'gif'].includes(fileExt)) {
                        currentDoc = '<p><strong>Dokumentasi saat ini:</strong></p><img src="../sw-content/patroli/' + patroli.dokumentasi + '" style="max-width: 200px; max-height: 150px;" class="img-thumbnail">';
                    } else if (['mp4', 'avi', 'mov'].includes(fileExt)) {
                        currentDoc = '<p><strong>Dokumentasi saat ini:</strong></p><video width="200" height="150" controls><source src="../sw-content/patroli/' + patroli.dokumentasi + '" type="video/' + fileExt + '"></video>';
                    } else {
                        currentDoc = '<p><strong>Dokumentasi saat ini:</strong></p><a href="../sw-content/patroli/' + patroli.dokumentasi + '" target="_blank" class="btn btn-sm btn-info">Lihat File</a>';
                    }
                    
                    $('#current-dokumentasi').html(currentDoc);
                } else {
                    $('#current-dokumentasi').html('<p class="text-muted">Tidak ada dokumentasi</p>');
                }
                
                $('#modal-edit-patroli').modal('show');
            } else {
                swal({
                    title: 'Error!', 
                    text: 'Data tidak ditemukan!', 
                    icon: 'error', 
                    timer: 1500
                });
            }
        },
        error: function() {
            swal({
                title: 'Error!', 
                text: 'Terjadi kesalahan sistem!', 
                icon: 'error', 
                timer: 1500
            });
        }
    });
}

// Function to delete patroli
function deletePatroli(id) {
    swal({
        title: 'Konfirmasi',
        text: 'Apakah Anda yakin ingin menghapus data patroli ini?',
        icon: 'warning',
        buttons: {
            cancel: {
                text: "Batal",
                value: null,
                visible: true,
                className: "",
                closeModal: true,
            },
            confirm: {
                text: "Ya, Hapus!",
                value: true,
                visible: true,
                className: "btn-danger",
                closeModal: true
            }
        }
    }).then((willDelete) => {
        if (willDelete) {
            $.ajax({
                url: './sw-mod/patroli/proses.php?action=delete&id=' + id,
                type: 'GET',
                success: function(data) {
                    if (data == 'success') {
                        swal({
                            title: 'Berhasil!', 
                            text: 'Data patroli berhasil dihapus!', 
                            icon: 'success', 
                            timer: 1500
                        });
                        $('#sw-datatable').DataTable().ajax.reload();
                    } else {
                        swal({
                            title: 'Oops!', 
                            text: data, 
                            icon: 'error', 
                            timer: 1500
                        });
                    }
                },
                error: function() {
                    swal({
                        title: 'Error!', 
                        text: 'Terjadi kesalahan sistem!', 
                        icon: 'error', 
                        timer: 1500
                    });
                }
            });
        }
    });
}

// Function to view documentation
function viewDokumentasi(filename) {
    if (!filename) {
        swal({
            title: 'Info', 
            text: 'Tidak ada dokumentasi untuk data ini', 
            icon: 'info', 
            timer: 1500
        });
        return;
    }
    
    var fileExt = filename.split('.').pop().toLowerCase();
    var fileUrl = '../sw-content/patroli/' + filename;
    
    if (['jpg', 'jpeg', 'png', 'gif'].includes(fileExt)) {
        // Show image in modal
        var imageModal = '<div class="modal fade" id="image-modal" tabindex="-1"><div class="modal-dialog modal-lg"><div class="modal-content"><div class="modal-header"><button type="button" class="close" data-dismiss="modal">&times;</button><h4 class="modal-title">Dokumentasi Patroli</h4></div><div class="modal-body text-center"><img src="' + fileUrl + '" style="max-width: 100%; height: auto;"></div></div></div></div>';
        $('body').append(imageModal);
        $('#image-modal').modal('show');
        $('#image-modal').on('hidden.bs.modal', function() {
            $(this).remove();
        });
    } else {
        // Open file in new tab
        window.open(fileUrl, '_blank');
    }
}
