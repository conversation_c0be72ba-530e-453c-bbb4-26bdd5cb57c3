<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>🔧 Fix Admin Login - Force Create Users</h2>";

// Include config
require_once 'sw-library/sw-config.php';

// Check database connection
if ($connection->connect_error) {
    echo "<p style='color:red'>❌ Database connection failed: " . $connection->connect_error . "</p>";
    exit;
}

echo "<p style='color:green'>✅ Database connected successfully</p>";

// Salt yang sama dengan sistem
$salt = '$%DSuTyr47542@#&*!=QxR094{a911}+';
$password_plain = 'eydcom';
$password_hash = hash('sha256', $salt . $password_plain);

echo "<h3>1. Password Information</h3>";
echo "<p>Plain Password: <code>$password_plain</code></p>";
echo "<p>Generated Hash: <code>$password_hash</code></p>";

// Hapus semua user yang ada
echo "<h3>2. Cleaning Database</h3>";
$connection->query("DELETE FROM user");
$connection->query("ALTER TABLE user AUTO_INCREMENT = 1");
echo "<p style='color:green'>✅ User table cleaned</p>";

// Buat user admin baru
echo "<h3>3. Creating Admin Users</h3>";

// User 1: Administrator
$admin_insert = "INSERT INTO user (
    user_id, username, email, password, fullname, 
    registered, created_login, last_login, session, 
    ip, browser, level
) VALUES (
    1, 
    'eydcom.com', 
    '<EMAIL>', 
    '$password_hash', 
    'EYD COMPUTER', 
    NOW(), 
    NOW(), 
    NOW(), 
    '-', 
    '127.0.0.1', 
    'Chrome', 
    1
)";

if ($connection->query($admin_insert)) {
    echo "<p style='color:green'>✅ Administrator created (ID: 1)</p>";
} else {
    echo "<p style='color:red'>❌ Error creating administrator: " . $connection->error . "</p>";
}

// User 2: Operator
$operator_insert = "INSERT INTO user (
    user_id, username, email, password, fullname, 
    registered, created_login, last_login, session, 
    ip, browser, level
) VALUES (
    2, 
    'operator', 
    '<EMAIL>', 
    '$password_hash', 
    'Operator', 
    NOW(), 
    NOW(), 
    NOW(), 
    '-', 
    '127.0.0.1', 
    'Chrome', 
    2
)";

if ($connection->query($operator_insert)) {
    echo "<p style='color:green'>✅ Operator created (ID: 2)</p>";
} else {
    echo "<p style='color:red'>❌ Error creating operator: " . $connection->error . "</p>";
}

// Verifikasi
echo "<h3>4. Verification</h3>";
$verify = $connection->query("SELECT user_id, username, email, fullname, level FROM user ORDER BY user_id");

if ($verify && $verify->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f0f0f0;'><th>ID</th><th>Username</th><th>Email</th><th>Fullname</th><th>Level</th></tr>";
    while ($row = $verify->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['user_id'] . "</td>";
        echo "<td><strong>" . $row['username'] . "</strong></td>";
        echo "<td>" . $row['email'] . "</td>";
        echo "<td>" . $row['fullname'] . "</td>";
        echo "<td>" . $row['level'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color:red'>❌ No users found after creation!</p>";
}

// Test login untuk kedua user
echo "<h3>5. Login Test</h3>";

$test_users = [
    'eydcom.com' => 'Administrator',
    'operator' => 'Operator'
];

foreach ($test_users as $test_username => $role) {
    $test_hash = hash('sha256', $salt . $password_plain);
    $login_test = "SELECT * FROM user WHERE username='$test_username' AND password='$test_hash'";
    $result = $connection->query($login_test);
    
    if ($result && $result->num_rows > 0) {
        $user_data = $result->fetch_assoc();
        echo "<div style='background: #d4edda; padding: 10px; margin: 5px 0; border-radius: 5px;'>";
        echo "<p style='color:green; margin:0;'><strong>✅ $role Login Test PASSED</strong></p>";
        echo "<p style='margin:5px 0 0 0;'>Username: <code>$test_username</code> | Password: <code>$password_plain</code></p>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 10px; margin: 5px 0; border-radius: 5px;'>";
        echo "<p style='color:red; margin:0;'><strong>❌ $role Login Test FAILED</strong></p>";
        echo "</div>";
    }
}

// Simulasi proses login seperti di login-proses.php
echo "<h3>6. Simulate Login Process</h3>";
$sim_username = 'eydcom.com';
$sim_password = 'eydcom';

echo "<p><strong>Simulating login with:</strong></p>";
echo "<p>Username: <code>$sim_username</code></p>";
echo "<p>Password: <code>$sim_password</code></p>";

// Proses seperti di login-proses.php
$username = htmlentities($sim_username);
$password = hash('sha256', $salt . $sim_password);
$query_login = "SELECT * FROM user WHERE username='$username' AND password='$password'";

echo "<p><strong>SQL Query:</strong> <code>$query_login</code></p>";

$result_login = $connection->query($query_login);
$login_num = $result_login->num_rows;

if ($login_num > 0) {
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px;'>";
    echo "<p style='color:green; margin:0;'><strong>🎉 LOGIN SIMULATION SUCCESSFUL!</strong></p>";
    echo "<p style='margin:10px 0 0 0;'>Response would be: <code>{\"response\":{\"error\": \"1\"}}</code></p>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
    echo "<p style='color:red; margin:0;'><strong>❌ LOGIN SIMULATION FAILED!</strong></p>";
    echo "<p style='margin:10px 0 0 0;'>Response would be: <code>{\"response\":{\"error\": \"0\"}}</code></p>";
    echo "</div>";
}

echo "<h3>7. 🚀 Ready to Login</h3>";
echo "<div style='background: #cce5ff; padding: 15px; border-radius: 5px;'>";
echo "<p><strong>Login URL:</strong></p>";
echo "<p><a href='sw-admin/login/' target='_blank'>http://localhost/SC%20ABSENSI%20SIGAP/ABSENSI/sw-admin/login/</a></p>";
echo "<br>";
echo "<p><strong>👑 Administrator:</strong></p>";
echo "<p>Username: <code>eydcom.com</code></p>";
echo "<p>Password: <code>eydcom</code></p>";
echo "<br>";
echo "<p><strong>👤 Operator:</strong></p>";
echo "<p>Username: <code>operator</code></p>";
echo "<p>Password: <code>eydcom</code></p>";
echo "</div>";

echo "<h3>8. Troubleshooting</h3>";
echo "<p>If login still fails, check:</p>";
echo "<ol>";
echo "<li>Browser console for JavaScript errors</li>";
echo "<li>Network tab to see if AJAX request is sent</li>";
echo "<li>Check if login-proses.php is accessible</li>";
echo "<li>Run <a href='test_login_process.php'>test_login_process.php</a> for detailed debugging</li>";
echo "</ol>";

$connection->close();
?>
