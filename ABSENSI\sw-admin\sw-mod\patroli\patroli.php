<?php 
if(empty($connection)){
  header('location:../../');
} else {
  // Proteksi akses - hanya untuk Operator (Level 2)
  if($level_user != '2') {
    echo '<div class="content-wrapper">
            <section class="content">
              <div class="row">
                <div class="col-xs-12">
                  <div class="box box-danger">
                    <div class="box-header with-border">
                      <h3 class="box-title">A<PERSON><PERSON></h3>
                    </div>
                    <div class="box-body">
                      <div class="alert alert-danger">
                        <h4><i class="icon fa fa-ban"></i> Aks<PERSON>!</h4>
                        Halaman ini hanya dapat diakses oleh <strong>Operator</strong>. 
                        Anda login sebagai <strong>Administrator</strong>.
                      </div>
                      <a href="./" class="btn btn-primary">
                        <i class="fa fa-arrow-left"></i> Kembali ke Dashboard
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </section>
          </div>';
    include_once 'sw-mod/sw-footer.php';
    exit;
  }
  
  include_once 'sw-mod/sw-panel.php';
echo'
  <div class="content-wrapper">';
switch(@$_GET['op']){ 
    default:
echo'
<section class="content-header">
  <h1>Data<small> Patroli</small></h1>
    <ol class="breadcrumb">
      <li><a href="./"><i class="fa fa-dashboard"></i> Beranda</a></li>
      <li class="active">Data Patroli</li>
    </ol>
</section>';
echo'
<section class="content">
  <div class="row">
    <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
      <div class="box box-solid">
        <div class="box-header with-border">
          <h3 class="box-title"><b>Data Patroli</b></h3>
          <div class="box-tools pull-right">
            <button type="button" class="btn btn-primary btn-sm" data-toggle="modal" data-target="#modal-add-patroli">
              <i class="fa fa-plus"></i> Tambah Patroli
            </button>
          </div>
        </div>
    <div class="box-body">
    <div class="table-responsive">
          <table id="sw-datatable" class="table table-bordered">
            <thead>
            <tr>
              <th style="width: 10px">No</th>
              <th>Tanggal</th>
              <th>Karyawan</th>
              <th>Lokasi</th>
              <th>Status</th>
              <th>Rating</th>
              <th>Dokumentasi</th>
              <th>Komentar</th>
              <th style="width:150px" class="text-center">Aksi</th>
            </tr>
            </thead>
            <tbody>
            <!-- Data akan dimuat via AJAX/DataTable -->
            </tbody>
            </table>
        </div>
          </div>
        </div>
      </div> 
    </section>';

// Modal Add Patroli
echo'
<!-- Modal Add Patroli -->
<div class="modal fade" id="modal-add-patroli" tabindex="-1" role="dialog">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
        <h4 class="modal-title">Tambah Data Patroli</h4>
      </div>
      <form class="add-patroli" method="POST" enctype="multipart/form-data">
        <div class="modal-body">
          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label>Karyawan <span class="text-red">*</span></label>
                <select name="id_karyawan" class="form-control" required>
                  <option value="">-- Pilih Karyawan --</option>
                </select>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group">
                <label>Lokasi <span class="text-red">*</span></label>
                <select name="id_lokasi" class="form-control" required>
                  <option value="">-- Pilih Lokasi --</option>
                </select>
              </div>
            </div>
          </div>
          
          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label>Tanggal Patroli <span class="text-red">*</span></label>
                <input type="datetime-local" name="tanggal" class="form-control" required>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group">
                <label>Status <span class="text-red">*</span></label>
                <select name="status" class="form-control" required>
                  <option value="">-- Pilih Status --</option>
                  <option value="Selesai">Selesai</option>
                  <option value="Dalam Proses">Dalam Proses</option>
                  <option value="Tertunda">Tertunda</option>
                  <option value="Dibatalkan">Dibatalkan</option>
                </select>
              </div>
            </div>
          </div>
          
          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label>Rating</label>
                <select name="rating" class="form-control">
                  <option value="">-- Pilih Rating --</option>
                  <option value="1">⭐ (1 - Buruk)</option>
                  <option value="2">⭐⭐ (2 - Kurang)</option>
                  <option value="3">⭐⭐⭐ (3 - Cukup)</option>
                  <option value="4">⭐⭐⭐⭐ (4 - Baik)</option>
                  <option value="5">⭐⭐⭐⭐⭐ (5 - Sangat Baik)</option>
                </select>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group">
                <label>ID Checklist</label>
                <input type="text" name="id_ceklis" class="form-control" placeholder="ID Checklist (opsional)">
              </div>
            </div>
          </div>
          
          <div class="form-group">
            <label>Dokumentasi</label>
            <input type="file" name="dokumentasi" class="form-control" accept="image/*,video/*,.pdf">
            <small class="text-muted">Format: JPG, PNG, MP4, PDF (Max: 10MB)</small>
          </div>
          
          <div class="form-group">
            <label>Komentar</label>
            <textarea name="komentar" class="form-control" rows="4" placeholder="Komentar atau catatan tambahan (opsional)"></textarea>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-default" data-dismiss="modal">Batal</button>
          <button type="submit" class="btn btn-primary">Simpan</button>
        </div>
      </form>
    </div>
  </div>
</div>';

// Modal Edit Patroli
echo'
<!-- Modal Edit Patroli -->
<div class="modal fade" id="modal-edit-patroli" tabindex="-1" role="dialog">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
        <h4 class="modal-title">Edit Data Patroli</h4>
      </div>
      <form class="edit-patroli" method="POST" enctype="multipart/form-data">
        <input type="hidden" name="id_patroli" id="edit-id-patroli">
        <div class="modal-body">
          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label>Karyawan <span class="text-red">*</span></label>
                <select name="id_karyawan" id="edit-id-karyawan" class="form-control" required>
                  <option value="">-- Pilih Karyawan --</option>
                </select>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group">
                <label>Lokasi <span class="text-red">*</span></label>
                <select name="id_lokasi" id="edit-id-lokasi" class="form-control" required>
                  <option value="">-- Pilih Lokasi --</option>
                </select>
              </div>
            </div>
          </div>
          
          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label>Tanggal Patroli <span class="text-red">*</span></label>
                <input type="datetime-local" name="tanggal" id="edit-tanggal" class="form-control" required>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group">
                <label>Status <span class="text-red">*</span></label>
                <select name="status" id="edit-status" class="form-control" required>
                  <option value="">-- Pilih Status --</option>
                  <option value="Selesai">Selesai</option>
                  <option value="Dalam Proses">Dalam Proses</option>
                  <option value="Tertunda">Tertunda</option>
                  <option value="Dibatalkan">Dibatalkan</option>
                </select>
              </div>
            </div>
          </div>
          
          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label>Rating</label>
                <select name="rating" id="edit-rating" class="form-control">
                  <option value="">-- Pilih Rating --</option>
                  <option value="1">⭐ (1 - Buruk)</option>
                  <option value="2">⭐⭐ (2 - Kurang)</option>
                  <option value="3">⭐⭐⭐ (3 - Cukup)</option>
                  <option value="4">⭐⭐⭐⭐ (4 - Baik)</option>
                  <option value="5">⭐⭐⭐⭐⭐ (5 - Sangat Baik)</option>
                </select>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group">
                <label>ID Checklist</label>
                <input type="text" name="id_ceklis" id="edit-id-ceklis" class="form-control" placeholder="ID Checklist (opsional)">
              </div>
            </div>
          </div>
          
          <div class="form-group">
            <label>Dokumentasi Baru</label>
            <input type="file" name="dokumentasi" class="form-control" accept="image/*,video/*,.pdf">
            <small class="text-muted">Kosongkan jika tidak ingin mengubah dokumentasi</small>
            <div id="current-dokumentasi" class="mt-2"></div>
          </div>
          
          <div class="form-group">
            <label>Komentar</label>
            <textarea name="komentar" id="edit-komentar" class="form-control" rows="4" placeholder="Komentar atau catatan tambahan (opsional)"></textarea>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-default" data-dismiss="modal">Batal</button>
          <button type="submit" class="btn btn-primary">Update</button>
        </div>
      </form>
    </div>
  </div>
</div>';

break;
}
echo'</div>';
echo'<script src="./sw-mod/patroli/scripts.js"></script>';
}?>
