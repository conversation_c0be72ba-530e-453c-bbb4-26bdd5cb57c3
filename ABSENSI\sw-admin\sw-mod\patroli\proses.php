<?php
session_start();
if(empty($_SESSION['SESSION_USER']) && empty($_SESSION['SESSION_ID'])){
    header('location:../../login/');
    exit;
}

require_once '../../../sw-library/sw-config.php';
require_once '../../login/login_session.php';
include('../../../sw-library/sw-function.php');

// Proteksi akses - hanya untuk Operator (Level 2)
if($level_user != '2') {
    echo 'Akses ditolak. Halaman ini hanya untuk Operator.';
    exit;
}

$action = isset($_GET['action']) ? $_GET['action'] : '';

switch($action) {
    case 'get_karyawan':
        // Get list karyawan untuk dropdown
        $query = "SELECT id, employees_code, employees_name FROM employees ORDER BY employees_name ASC";
        $result = $connection->query($query);
        
        $data = array();
        if($result && $result->num_rows > 0) {
            while($row = $result->fetch_assoc()) {
                $data[] = $row;
            }
        }
        
        header('Content-Type: application/json');
        echo json_encode($data);
        break;
        
    case 'get_lokasi':
        // Get list lokasi untuk dropdown
        $query = "SELECT building_id, building_name FROM building ORDER BY building_name ASC";
        $result = $connection->query($query);
        
        $data = array();
        if($result && $result->num_rows > 0) {
            while($row = $result->fetch_assoc()) {
                $data[] = $row;
            }
        }
        
        header('Content-Type: application/json');
        echo json_encode($data);
        break;
        
    case 'add':
        // Tambah data patroli
        $error = array();
        
        // Validasi input
        if(empty($_POST['id_karyawan'])) {
            $error[] = 'Karyawan harus dipilih';
        } else {
            $id_karyawan = mysqli_real_escape_string($connection, $_POST['id_karyawan']);
        }
        
        if(empty($_POST['id_lokasi'])) {
            $error[] = 'Lokasi harus dipilih';
        } else {
            $id_lokasi = mysqli_real_escape_string($connection, $_POST['id_lokasi']);
        }
        
        if(empty($_POST['tanggal'])) {
            $error[] = 'Tanggal harus diisi';
        } else {
            $tanggal = mysqli_real_escape_string($connection, $_POST['tanggal']);
        }
        
        if(empty($_POST['status'])) {
            $error[] = 'Status harus dipilih';
        } else {
            $status = mysqli_real_escape_string($connection, $_POST['status']);
        }
        
        $rating = !empty($_POST['rating']) ? mysqli_real_escape_string($connection, $_POST['rating']) : null;
        $id_ceklis = !empty($_POST['id_ceklis']) ? mysqli_real_escape_string($connection, $_POST['id_ceklis']) : null;
        $komentar = !empty($_POST['komentar']) ? mysqli_real_escape_string($connection, $_POST['komentar']) : null;
        
        // Handle file upload
        $dokumentasi = null;
        if(isset($_FILES['dokumentasi']) && $_FILES['dokumentasi']['error'] == 0) {
            $allowed_types = array('jpg', 'jpeg', 'png', 'gif', 'mp4', 'avi', 'mov', 'pdf');
            $file_extension = strtolower(pathinfo($_FILES['dokumentasi']['name'], PATHINFO_EXTENSION));
            
            if(in_array($file_extension, $allowed_types)) {
                if($_FILES['dokumentasi']['size'] <= 10485760) { // 10MB
                    $upload_dir = '../../../sw-content/patroli/';
                    if(!is_dir($upload_dir)) {
                        mkdir($upload_dir, 0755, true);
                    }
                    
                    $filename = 'patroli_' . time() . '_' . rand(1000, 9999) . '.' . $file_extension;
                    $upload_path = $upload_dir . $filename;
                    
                    if(move_uploaded_file($_FILES['dokumentasi']['tmp_name'], $upload_path)) {
                        $dokumentasi = $filename;
                    } else {
                        $error[] = 'Gagal mengupload file dokumentasi';
                    }
                } else {
                    $error[] = 'Ukuran file dokumentasi terlalu besar (max 10MB)';
                }
            } else {
                $error[] = 'Format file dokumentasi tidak didukung';
            }
        }
        
        if(empty($error)) {
            // Untuk sementara, karena tabel belum dibuat, kita simulasikan success
            // Uncomment code di bawah setelah tabel patroli dibuat

            /*
            $query = "INSERT INTO patroli (
                id_karyawan,
                id_lokasi,
                tanggal,
                status,
                rating,
                id_ceklis,
                komentar,
                dokumentasi
            ) VALUES (
                '$id_karyawan',
                '$id_lokasi',
                '$tanggal',
                '$status',
                " . ($rating ? "'$rating'" : "NULL") . ",
                " . ($id_ceklis ? "'$id_ceklis'" : "NULL") . ",
                " . ($komentar ? "'$komentar'" : "NULL") . ",
                " . ($dokumentasi ? "'$dokumentasi'" : "NULL") . "
            )";

            if($connection->query($query)) {
                echo 'success';
            } else {
                echo 'Gagal menyimpan data: ' . $connection->error;
            }
            */

            // Simulasi success untuk testing UI
            echo 'success';
        } else {
            echo implode(', ', $error);
        }
        break;
        
    case 'get_detail':
        // Get detail patroli untuk edit
        $id = isset($_GET['id']) ? intval($_GET['id']) : 0;

        if($id > 0) {
            // Simulasi data untuk testing UI (karena tabel belum dibuat)
            $dummy_data = array(
                'id_patroli' => $id,
                'id_karyawan' => '17',
                'id_lokasi' => '8',
                'tanggal' => '2024-01-15T08:00',
                'status' => 'Selesai',
                'rating' => '5',
                'id_ceklis' => 'CHK001',
                'komentar' => 'Patroli rutin pagi, semua kondisi normal',
                'dokumentasi' => 'patroli_sample.jpg'
            );
            echo json_encode(array('status' => 'success', 'data' => $dummy_data));

            /*
            // Uncomment setelah tabel dibuat
            $query = "SELECT * FROM patroli WHERE id_patroli = $id";
            $result = $connection->query($query);

            if($result && $result->num_rows > 0) {
                $data = $result->fetch_assoc();
                echo json_encode(array('status' => 'success', 'data' => $data));
            } else {
                echo json_encode(array('status' => 'error', 'message' => 'Data tidak ditemukan'));
            }
            */
        } else {
            echo json_encode(array('status' => 'error', 'message' => 'ID tidak valid'));
        }
        break;
        
    case 'edit':
        // Edit data patroli
        $error = array();
        
        if(empty($_POST['id_patroli'])) {
            $error[] = 'ID Patroli tidak valid';
        } else {
            $id_patroli = intval($_POST['id_patroli']);
        }
        
        // Validasi input (sama seperti add)
        if(empty($_POST['id_karyawan'])) {
            $error[] = 'Karyawan harus dipilih';
        } else {
            $id_karyawan = mysqli_real_escape_string($connection, $_POST['id_karyawan']);
        }
        
        if(empty($_POST['id_lokasi'])) {
            $error[] = 'Lokasi harus dipilih';
        } else {
            $id_lokasi = mysqli_real_escape_string($connection, $_POST['id_lokasi']);
        }
        
        if(empty($_POST['tanggal'])) {
            $error[] = 'Tanggal harus diisi';
        } else {
            $tanggal = mysqli_real_escape_string($connection, $_POST['tanggal']);
        }
        
        if(empty($_POST['status'])) {
            $error[] = 'Status harus dipilih';
        } else {
            $status = mysqli_real_escape_string($connection, $_POST['status']);
        }
        
        $rating = !empty($_POST['rating']) ? mysqli_real_escape_string($connection, $_POST['rating']) : null;
        $id_ceklis = !empty($_POST['id_ceklis']) ? mysqli_real_escape_string($connection, $_POST['id_ceklis']) : null;
        $komentar = !empty($_POST['komentar']) ? mysqli_real_escape_string($connection, $_POST['komentar']) : null;
        
        // Handle file upload untuk update
        $dokumentasi_update = '';
        if(isset($_FILES['dokumentasi']) && $_FILES['dokumentasi']['error'] == 0) {
            $allowed_types = array('jpg', 'jpeg', 'png', 'gif', 'mp4', 'avi', 'mov', 'pdf');
            $file_extension = strtolower(pathinfo($_FILES['dokumentasi']['name'], PATHINFO_EXTENSION));
            
            if(in_array($file_extension, $allowed_types)) {
                if($_FILES['dokumentasi']['size'] <= 10485760) { // 10MB
                    $upload_dir = '../../../sw-content/patroli/';
                    if(!is_dir($upload_dir)) {
                        mkdir($upload_dir, 0755, true);
                    }
                    
                    $filename = 'patroli_' . time() . '_' . rand(1000, 9999) . '.' . $file_extension;
                    $upload_path = $upload_dir . $filename;
                    
                    if(move_uploaded_file($_FILES['dokumentasi']['tmp_name'], $upload_path)) {
                        // Hapus file lama jika ada
                        $old_query = "SELECT dokumentasi FROM patroli WHERE id_patroli = $id_patroli";
                        $old_result = $connection->query($old_query);
                        if($old_result && $old_result->num_rows > 0) {
                            $old_data = $old_result->fetch_assoc();
                            if($old_data['dokumentasi'] && file_exists($upload_dir . $old_data['dokumentasi'])) {
                                unlink($upload_dir . $old_data['dokumentasi']);
                            }
                        }
                        
                        $dokumentasi_update = ", dokumentasi = '$filename'";
                    } else {
                        $error[] = 'Gagal mengupload file dokumentasi';
                    }
                } else {
                    $error[] = 'Ukuran file dokumentasi terlalu besar (max 10MB)';
                }
            } else {
                $error[] = 'Format file dokumentasi tidak didukung';
            }
        }
        
        if(empty($error)) {
            // Simulasi success untuk testing UI
            echo 'success';

            /*
            // Uncomment setelah tabel dibuat
            $query = "UPDATE patroli SET
                id_karyawan = '$id_karyawan',
                id_lokasi = '$id_lokasi',
                tanggal = '$tanggal',
                status = '$status',
                rating = " . ($rating ? "'$rating'" : "NULL") . ",
                id_ceklis = " . ($id_ceklis ? "'$id_ceklis'" : "NULL") . ",
                komentar = " . ($komentar ? "'$komentar'" : "NULL") . "
                $dokumentasi_update
                WHERE id_patroli = $id_patroli";

            if($connection->query($query)) {
                echo 'success';
            } else {
                echo 'Gagal mengupdate data: ' . $connection->error;
            }
            */
        } else {
            echo implode(', ', $error);
        }
        break;
        
    case 'delete':
        // Hapus data patroli
        $id = isset($_GET['id']) ? intval($_GET['id']) : 0;

        if($id > 0) {
            // Simulasi success untuk testing UI
            echo 'success';

            /*
            // Uncomment setelah tabel dibuat
            // Hapus file dokumentasi jika ada
            $file_query = "SELECT dokumentasi FROM patroli WHERE id_patroli = $id";
            $file_result = $connection->query($file_query);
            if($file_result && $file_result->num_rows > 0) {
                $file_data = $file_result->fetch_assoc();
                if($file_data['dokumentasi']) {
                    $file_path = '../../../sw-content/patroli/' . $file_data['dokumentasi'];
                    if(file_exists($file_path)) {
                        unlink($file_path);
                    }
                }
            }

            $query = "DELETE FROM patroli WHERE id_patroli = $id";
            if($connection->query($query)) {
                echo 'success';
            } else {
                echo 'Gagal menghapus data: ' . $connection->error;
            }
            */
        } else {
            echo 'ID tidak valid';
        }
        break;
        
    default:
        echo 'Action tidak valid';
        break;
}

$connection->close();
?>
