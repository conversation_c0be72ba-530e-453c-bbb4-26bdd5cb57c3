<?php
session_start();
if(empty($_SESSION['SESSION_USER']) && empty($_SESSION['SESSION_ID'])){
    header('location:../../login/');
    exit;
}

require_once '../../../sw-library/sw-config.php';
require_once '../../login/login_session.php';
include('../../../sw-library/sw-function.php');

// Proteksi akses - hanya untuk Operator (Level 2)
if($level_user != '2') {
    echo json_encode(array(
        "sEcho" => 0,
        "iTotalRecords" => 0,
        "iTotalDisplayRecords" => 0,
        "aaData" => array(),
        "error" => "Akses ditolak. Halaman ini hanya untuk Operator."
    ));
    exit;
}

// Untuk sementara, gunakan data dummy karena tabel belum dibuat
// Uncomment code di bawah setelah tabel patroli dibuat

/*
// DataTable server-side processing
$aColumns = [
    'p.id_patroli',
    'p.tanggal',
    'e.employees_name',
    'b.building_name',
    'p.status',
    'p.rating',
    'p.dokumentasi',
    'p.komentar',
    'p.id_patroli' // untuk aksi
];

$sIndexColumn = "p.id_patroli";
$sTable = "patroli p
           LEFT JOIN employees e ON p.id_karyawan = e.id
           LEFT JOIN building b ON p.id_lokasi = b.building_id";
*/

// Data dummy untuk testing UI
$dummy_data = [
    [
        'id_patroli' => 1,
        'tanggal' => '2024-01-15 08:00:00',
        'employees_name' => 'ESTRI YASRIATNI',
        'building_name' => 'Gedung Utama',
        'status' => 'Selesai',
        'rating' => 5,
        'dokumentasi' => 'patroli_sample1.jpg',
        'komentar' => 'Patroli rutin pagi, semua kondisi normal'
    ],
    [
        'id_patroli' => 2,
        'tanggal' => '2024-01-15 14:30:00',
        'employees_name' => 'CITRA ANINDA',
        'building_name' => 'Gedung Parkir',
        'status' => 'Dalam Proses',
        'rating' => 4,
        'dokumentasi' => null,
        'komentar' => 'Patroli siang, ditemukan beberapa hal yang perlu diperbaiki'
    ],
    [
        'id_patroli' => 3,
        'tanggal' => '2024-01-15 20:00:00',
        'employees_name' => 'User',
        'building_name' => 'Area Keamanan',
        'status' => 'Tertunda',
        'rating' => null,
        'dokumentasi' => 'patroli_sample3.mp4',
        'komentar' => 'Patroli malam ditunda karena cuaca buruk'
    ]
];

$iTotal = count($dummy_data);
$iFilteredTotal = $iTotal;

// Output
$output = array(
    "sEcho" => intval($_POST['sEcho']),
    "iTotalRecords" => $iTotal,
    "iTotalDisplayRecords" => $iFilteredTotal,
    "aaData" => array()
);

$no = 1;
foreach($dummy_data as $aRow) {
    $row = array();

    // No
    $row[] = $no++;

    // Tanggal
    $tanggal = date('d/m/Y H:i', strtotime($aRow['tanggal']));
    $row[] = $tanggal;

    // Karyawan
    $row[] = $aRow['employees_name'] ? $aRow['employees_name'] : '-';

    // Lokasi
    $row[] = $aRow['building_name'] ? $aRow['building_name'] : '-';

    // Status
    $status_class = '';
    switch($aRow['status']) {
        case 'Selesai':
            $status_class = 'label-success';
            break;
        case 'Dalam Proses':
            $status_class = 'label-warning';
            break;
        case 'Tertunda':
            $status_class = 'label-info';
            break;
        case 'Dibatalkan':
            $status_class = 'label-danger';
            break;
        default:
            $status_class = 'label-default';
    }
    $row[] = '<span class="label ' . $status_class . '">' . $aRow['status'] . '</span>';

    // Rating
    if($aRow['rating']) {
        $stars = str_repeat('⭐', intval($aRow['rating']));
        $row[] = $stars . ' (' . $aRow['rating'] . ')';
    } else {
        $row[] = '-';
    }

    // Dokumentasi
    if($aRow['dokumentasi']) {
        $file_ext = strtolower(pathinfo($aRow['dokumentasi'], PATHINFO_EXTENSION));
        if(in_array($file_ext, ['jpg', 'jpeg', 'png', 'gif'])) {
            $doc_btn = '<button type="button" class="btn btn-xs btn-info" onclick="viewDokumentasi(\'' . $aRow['dokumentasi'] . '\')">
                        <i class="fa fa-image"></i> Lihat
                        </button>';
        } elseif(in_array($file_ext, ['mp4', 'avi', 'mov'])) {
            $doc_btn = '<button type="button" class="btn btn-xs btn-info" onclick="viewDokumentasi(\'' . $aRow['dokumentasi'] . '\')">
                        <i class="fa fa-video-camera"></i> Lihat
                        </button>';
        } else {
            $doc_btn = '<button type="button" class="btn btn-xs btn-info" onclick="viewDokumentasi(\'' . $aRow['dokumentasi'] . '\')">
                        <i class="fa fa-file"></i> Lihat
                        </button>';
        }
        $row[] = $doc_btn;
    } else {
        $row[] = '<span class="text-muted">-</span>';
    }

    // Komentar
    if($aRow['komentar']) {
        $komentar = strlen($aRow['komentar']) > 50 ?
                   substr($aRow['komentar'], 0, 50) . '...' :
                   $aRow['komentar'];
        $row[] = '<span title="' . htmlspecialchars($aRow['komentar']) . '">' . htmlspecialchars($komentar) . '</span>';
    } else {
        $row[] = '<span class="text-muted">-</span>';
    }

    // Aksi
    $aksi = '<div class="btn-group">
                <button type="button" class="btn btn-xs btn-warning" onclick="editPatroli(' . $aRow['id_patroli'] . ')" title="Edit">
                    <i class="fa fa-edit"></i>
                </button>
                <button type="button" class="btn btn-xs btn-danger" onclick="deletePatroli(' . $aRow['id_patroli'] . ')" title="Hapus">
                    <i class="fa fa-trash"></i>
                </button>
             </div>';
    $row[] = $aksi;

    $output['aaData'][] = $row;
}

echo json_encode($output);
?>
