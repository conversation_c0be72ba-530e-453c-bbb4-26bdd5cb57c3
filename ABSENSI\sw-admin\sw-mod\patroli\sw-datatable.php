<?php
session_start();
if(empty($_SESSION['SESSION_USER']) && empty($_SESSION['SESSION_ID'])){
    header('location:../../login/');
    exit;
}

require_once '../../../sw-library/sw-config.php';
require_once '../../login/login_session.php';
include('../../../sw-library/sw-function.php');

// Proteksi akses - hanya untuk Operator (Level 2)
if($level_user != '2') {
    echo json_encode(array(
        "sEcho" => 0,
        "iTotalRecords" => 0,
        "iTotalDisplayRecords" => 0,
        "aaData" => array(),
        "error" => "Akses ditolak. Halaman ini hanya untuk Operator."
    ));
    exit;
}

// DataTable server-side processing
$aColumns = [
    'p.id_patroli',
    'p.tanggal', 
    'e.employees_name',
    'b.name',
    'p.status',
    'p.rating',
    'p.dokumentasi',
    'p.komentar',
    'p.id_patroli' // untuk aksi
];

$sIndexColumn = "p.id_patroli";
$sTable = "patroli p 
           LEFT JOIN employees e ON p.id_karyawan = e.id 
           LEFT JOIN building b ON p.id_lokasi = b.building_id";

// Input parameters
$sLimit = "";
if(isset($_POST['iDisplayStart']) && $_POST['iDisplayLength'] != '-1') {
    $sLimit = "LIMIT " . intval($_POST['iDisplayStart']) . ", " . intval($_POST['iDisplayLength']);
}

$sOrder = "";
if(isset($_POST['iSortCol_0'])) {
    $sOrder = "ORDER BY ";
    for($i = 0; $i < intval($_POST['iSortingCols']); $i++) {
        if($_POST['bSortable_' . intval($_POST['iSortCol_' . $i])] == "true") {
            if(isset($aColumns[intval($_POST['iSortCol_' . $i])])) {
                $sOrder .= $aColumns[intval($_POST['iSortCol_' . $i])] . " " . 
                          ($_POST['sSortDir_' . $i] === 'asc' ? 'asc' : 'desc') . ", ";
            }
        }
    }
    $sOrder = substr_replace($sOrder, "", -2);
    if($sOrder == "ORDER BY") {
        $sOrder = "ORDER BY p.tanggal DESC";
    }
} else {
    $sOrder = "ORDER BY p.tanggal DESC";
}

$sWhere = "";
if(isset($_POST['sSearch']) && $_POST['sSearch'] != "") {
    $sWhere = "WHERE (";
    $search_term = mysqli_real_escape_string($connection, $_POST['sSearch']);
    $sWhere .= "e.employees_name LIKE '%$search_term%' OR ";
    $sWhere .= "b.name LIKE '%$search_term%' OR ";
    $sWhere .= "p.status LIKE '%$search_term%' OR ";
    $sWhere .= "p.komentar LIKE '%$search_term%'";
    $sWhere .= ')';
}

// Main query
$sQuery = "SELECT p.id_patroli, p.tanggal, e.employees_name, b.name as building_name, b.address as building_address, 
                  p.status, p.rating, p.dokumentasi, p.komentar
           FROM $sTable
           $sWhere
           $sOrder
           $sLimit";

$rResult = $connection->query($sQuery);

// Total records after filtering
if($sWhere != "") {
    $sQueryCount = "SELECT COUNT(*) as total FROM $sTable $sWhere";
    $rResultFilterTotal = $connection->query($sQueryCount);
    $aResultFilterTotal = $rResultFilterTotal->fetch_assoc();
    $iFilteredTotal = $aResultFilterTotal['total'];
} else {
    $sQueryCount = "SELECT COUNT(*) as total FROM $sTable";
    $rResultTotal = $connection->query($sQueryCount);
    $aResultTotal = $rResultTotal->fetch_assoc();
    $iFilteredTotal = $aResultTotal['total'];
}

// Total records
$sQueryTotal = "SELECT COUNT(*) as total FROM $sTable";
$rResultTotal = $connection->query($sQueryTotal);
$aResultTotal = $rResultTotal->fetch_assoc();
$iTotal = $aResultTotal['total'];

// Output
$output = array(
    "sEcho" => intval($_POST['sEcho']),
    "iTotalRecords" => $iTotal,
    "iTotalDisplayRecords" => $iFilteredTotal,
    "aaData" => array()
);

$no = $_POST['iDisplayStart'] + 1;
while($aRow = $rResult->fetch_assoc()) {
    $row = array();
    
    // No
    $row[] = $no++;
    
    // Tanggal
    $tanggal = date('d/m/Y H:i', strtotime($aRow['tanggal']));
    $row[] = $tanggal;
    
    // Karyawan
    $row[] = $aRow['employees_name'] ? $aRow['employees_name'] : '-';
    
    // Lokasi
    if($aRow['building_name']) {
        $lokasi_display = '<strong>' . htmlspecialchars($aRow['building_name']) . '</strong>';
        if(isset($aRow['building_address']) && $aRow['building_address']) {
            $lokasi_display .= '<br><small class="text-muted">' . htmlspecialchars($aRow['building_address']) . '</small>';
        }
        $row[] = $lokasi_display;
    } else {
        $row[] = '-';
    }
    
    // Status
    $status_class = '';
    switch($aRow['status']) {
        case 'Selesai':
            $status_class = 'label-success';
            break;
        case 'Dalam Proses':
            $status_class = 'label-warning';
            break;
        case 'Tertunda':
            $status_class = 'label-info';
            break;
        case 'Dibatalkan':
            $status_class = 'label-danger';
            break;
        default:
            $status_class = 'label-default';
    }
    $row[] = '<span class="label ' . $status_class . '">' . $aRow['status'] . '</span>';
    
    // Rating
    if($aRow['rating']) {
        $stars = str_repeat('⭐', intval($aRow['rating']));
        $row[] = $stars . ' (' . $aRow['rating'] . ')';
    } else {
        $row[] = '-';
    }
    
    // Dokumentasi
    if($aRow['dokumentasi']) {
        $file_ext = strtolower(pathinfo($aRow['dokumentasi'], PATHINFO_EXTENSION));
        if(in_array($file_ext, ['jpg', 'jpeg', 'png', 'gif'])) {
            $doc_btn = '<button type="button" class="btn btn-xs btn-info" onclick="viewDokumentasi(\'' . $aRow['dokumentasi'] . '\')">
                        <i class="fa fa-image"></i> Lihat
                        </button>';
        } elseif(in_array($file_ext, ['mp4', 'avi', 'mov'])) {
            $doc_btn = '<button type="button" class="btn btn-xs btn-info" onclick="viewDokumentasi(\'' . $aRow['dokumentasi'] . '\')">
                        <i class="fa fa-video-camera"></i> Lihat
                        </button>';
        } else {
            $doc_btn = '<button type="button" class="btn btn-xs btn-info" onclick="viewDokumentasi(\'' . $aRow['dokumentasi'] . '\')">
                        <i class="fa fa-file"></i> Lihat
                        </button>';
        }
        $row[] = $doc_btn;
    } else {
        $row[] = '<span class="text-muted">-</span>';
    }
    
    // Komentar
    if($aRow['komentar']) {
        $komentar = strlen($aRow['komentar']) > 50 ? 
                   substr($aRow['komentar'], 0, 50) . '...' : 
                   $aRow['komentar'];
        $row[] = '<span title="' . htmlspecialchars($aRow['komentar']) . '">' . htmlspecialchars($komentar) . '</span>';
    } else {
        $row[] = '<span class="text-muted">-</span>';
    }
    
    // Aksi
    $aksi = '<div class="btn-group">
                <button type="button" class="btn btn-xs btn-warning" onclick="editPatroli(' . $aRow['id_patroli'] . ')" title="Edit">
                    <i class="fa fa-edit"></i>
                </button>
                <button type="button" class="btn btn-xs btn-danger" onclick="deletePatroli(' . $aRow['id_patroli'] . ')" title="Hapus">
                    <i class="fa fa-trash"></i>
                </button>
             </div>';
    $row[] = $aksi;
    
    $output['aaData'][] = $row;
}

echo json_encode($output);
?>
