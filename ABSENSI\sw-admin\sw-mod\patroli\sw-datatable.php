<?php
session_start();
if(empty($_SESSION['SESSION_USER']) && empty($_SESSION['SESSION_ID'])){
    header('location:../../login/');
    exit;
}

require_once '../../../sw-library/sw-config.php';
require_once '../../login/login_session.php';
include('../../../sw-library/sw-function.php');

// DataTable server-side processing
$aColumns = [
    'p.id_patroli',
    'p.tanggal', 
    'e.employees_name',
    'b.building_name',
    'p.status',
    'p.rating',
    'p.dokumentasi',
    'p.komentar',
    'p.id_patroli' // untuk aksi
];

$sIndexColumn = "p.id_patroli";
$sTable = "patroli p 
           LEFT JOIN employees e ON p.id_karyawan = e.id 
           LEFT JOIN building b ON p.id_lokasi = b.building_id";

// Input parameters
$sLimit = "";
if(isset($_POST['iDisplayStart']) && $_POST['iDisplayLength'] != '-1') {
    $sLimit = "LIMIT " . intval($_POST['iDisplayStart']) . ", " . intval($_POST['iDisplayLength']);
}

$sOrder = "";
if(isset($_POST['iSortCol_0'])) {
    $sOrder = "ORDER BY ";
    for($i = 0; $i < intval($_POST['iSortingCols']); $i++) {
        if($_POST['bSortable_' . intval($_POST['iSortCol_' . $i])] == "true") {
            if(isset($aColumns[intval($_POST['iSortCol_' . $i])])) {
                $sOrder .= $aColumns[intval($_POST['iSortCol_' . $i])] . " " . 
                          ($_POST['sSortDir_' . $i] === 'asc' ? 'asc' : 'desc') . ", ";
            }
        }
    }
    $sOrder = substr_replace($sOrder, "", -2);
    if($sOrder == "ORDER BY") {
        $sOrder = "";
    }
}

$sWhere = "";
if(isset($_POST['sSearch']) && $_POST['sSearch'] != "") {
    $sWhere = "WHERE (";
    for($i = 0; $i < count($aColumns); $i++) {
        if(isset($_POST['bSearchable_' . $i]) && $_POST['bSearchable_' . $i] == "true") {
            $sWhere .= $aColumns[$i] . " LIKE '%" . mysqli_real_escape_string($connection, $_POST['sSearch']) . "%' OR ";
        }
    }
    $sWhere = substr_replace($sWhere, "", -3);
    $sWhere .= ')';
}

// Individual column filtering
for($i = 0; $i < count($aColumns); $i++) {
    if(isset($_POST['bSearchable_' . $i]) && $_POST['bSearchable_' . $i] == "true" && $_POST['sSearch_' . $i] != '') {
        if($sWhere == "") {
            $sWhere = "WHERE ";
        } else {
            $sWhere .= " AND ";
        }
        $sWhere .= $aColumns[$i] . " LIKE '%" . mysqli_real_escape_string($connection, $_POST['sSearch_' . $i]) . "%' ";
    }
}

// Main query
$sQuery = "SELECT SQL_CALC_FOUND_ROWS " . str_replace(" , ", " ", implode(", ", $aColumns)) . "
           FROM $sTable
           $sWhere
           $sOrder
           $sLimit";

$rResult = $connection->query($sQuery) or die($connection->error);

// Total records after filtering
$sQuery = "SELECT FOUND_ROWS()";
$rResultFilterTotal = $connection->query($sQuery) or die($connection->error);
$aResultFilterTotal = $rResultFilterTotal->fetch_array();
$iFilteredTotal = $aResultFilterTotal[0];

// Total records
$sQuery = "SELECT COUNT(*) FROM $sTable";
$rResultTotal = $connection->query($sQuery) or die($connection->error);
$aResultTotal = $rResultTotal->fetch_array();
$iTotal = $aResultTotal[0];

// Output
$output = array(
    "sEcho" => intval($_POST['sEcho']),
    "iTotalRecords" => $iTotal,
    "iTotalDisplayRecords" => $iFilteredTotal,
    "aaData" => array()
);

$no = $_POST['iDisplayStart'] + 1;
while($aRow = $rResult->fetch_array()) {
    $row = array();
    
    // No
    $row[] = $no++;
    
    // Tanggal
    $tanggal = date('d/m/Y H:i', strtotime($aRow['tanggal']));
    $row[] = $tanggal;
    
    // Karyawan
    $row[] = $aRow['employees_name'] ? $aRow['employees_name'] : '-';
    
    // Lokasi
    $row[] = $aRow['building_name'] ? $aRow['building_name'] : '-';
    
    // Status
    $status_class = '';
    switch($aRow['status']) {
        case 'Selesai':
            $status_class = 'label-success';
            break;
        case 'Dalam Proses':
            $status_class = 'label-warning';
            break;
        case 'Tertunda':
            $status_class = 'label-info';
            break;
        case 'Dibatalkan':
            $status_class = 'label-danger';
            break;
        default:
            $status_class = 'label-default';
    }
    $row[] = '<span class="label ' . $status_class . '">' . $aRow['status'] . '</span>';
    
    // Rating
    if($aRow['rating']) {
        $stars = str_repeat('⭐', intval($aRow['rating']));
        $row[] = $stars . ' (' . $aRow['rating'] . ')';
    } else {
        $row[] = '-';
    }
    
    // Dokumentasi
    if($aRow['dokumentasi']) {
        $file_ext = strtolower(pathinfo($aRow['dokumentasi'], PATHINFO_EXTENSION));
        if(in_array($file_ext, ['jpg', 'jpeg', 'png', 'gif'])) {
            $doc_btn = '<button type="button" class="btn btn-xs btn-info" onclick="viewDokumentasi(\'' . $aRow['dokumentasi'] . '\')">
                        <i class="fa fa-image"></i> Lihat
                        </button>';
        } elseif(in_array($file_ext, ['mp4', 'avi', 'mov'])) {
            $doc_btn = '<button type="button" class="btn btn-xs btn-info" onclick="viewDokumentasi(\'' . $aRow['dokumentasi'] . '\')">
                        <i class="fa fa-video-camera"></i> Lihat
                        </button>';
        } else {
            $doc_btn = '<button type="button" class="btn btn-xs btn-info" onclick="viewDokumentasi(\'' . $aRow['dokumentasi'] . '\')">
                        <i class="fa fa-file"></i> Lihat
                        </button>';
        }
        $row[] = $doc_btn;
    } else {
        $row[] = '<span class="text-muted">-</span>';
    }
    
    // Komentar
    if($aRow['komentar']) {
        $komentar = strlen($aRow['komentar']) > 50 ? 
                   substr($aRow['komentar'], 0, 50) . '...' : 
                   $aRow['komentar'];
        $row[] = '<span title="' . htmlspecialchars($aRow['komentar']) . '">' . htmlspecialchars($komentar) . '</span>';
    } else {
        $row[] = '<span class="text-muted">-</span>';
    }
    
    // Aksi
    $aksi = '<div class="btn-group">
                <button type="button" class="btn btn-xs btn-warning" onclick="editPatroli(' . $aRow['id_patroli'] . ')" title="Edit">
                    <i class="fa fa-edit"></i>
                </button>
                <button type="button" class="btn btn-xs btn-danger" onclick="deletePatroli(' . $aRow['id_patroli'] . ')" title="Hapus">
                    <i class="fa fa-trash"></i>
                </button>
             </div>';
    $row[] = $aksi;
    
    $output['aaData'][] = $row;
}

echo json_encode($output);
?>
