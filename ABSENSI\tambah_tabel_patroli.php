<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>🚀 Tambah Tabel Patroli ke Database</h2>";

// Include config
require_once 'sw-library/sw-config.php';

echo "<h3>1. 🔌 Koneksi Database</h3>";
if ($connection->connect_error) {
    echo "<p style='color:red'>❌ Koneksi database gagal: " . $connection->connect_error . "</p>";
    exit;
} else {
    echo "<p style='color:green'>✅ Koneksi database berhasil</p>";
    echo "<p><strong>Database:</strong> " . DB_NAME . "</p>";
}

echo "<h3>2. 🗂️ Cek Tabel yang Ada</h3>";
$existing_tables = $connection->query("SHOW TABLES");
$tables = [];
while ($row = $existing_tables->fetch_array()) {
    $tables[] = $row[0];
}

echo "<p><strong>Tabel yang tersedia:</strong> " . implode(', ', $tables) . "</p>";

// Cek apakah tabel patroli sudah ada
if (in_array('patroli', $tables)) {
    echo "<p style='color:orange'>⚠️ Tabel 'patroli' sudah ada!</p>";
    echo "<p>Apakah Anda ingin menghapus dan membuat ulang? <a href='?recreate=yes' style='color:red'>Ya, Hapus dan Buat Ulang</a></p>";
    
    if (isset($_GET['recreate']) && $_GET['recreate'] == 'yes') {
        $connection->query("DROP TABLE IF EXISTS patroli");
        echo "<p style='color:green'>✅ Tabel lama berhasil dihapus</p>";
    } else {
        echo "<h3>📊 Data Patroli yang Ada</h3>";
        $existing_data = $connection->query("SELECT * FROM patroli ORDER BY tanggal DESC LIMIT 10");
        if ($existing_data && $existing_data->num_rows > 0) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr style='background: #f0f0f0;'>";
            echo "<th>ID</th><th>Dokumentasi</th><th>Tanggal</th><th>Status</th><th>Rating</th><th>Karyawan</th><th>Lokasi</th>";
            echo "</tr>";
            while ($row = $existing_data->fetch_assoc()) {
                echo "<tr>";
                echo "<td>" . $row['id_patroli'] . "</td>";
                echo "<td>" . ($row['dokumentasi'] ?? '-') . "</td>";
                echo "<td>" . $row['tanggal'] . "</td>";
                echo "<td>" . $row['status'] . "</td>";
                echo "<td>" . ($row['rating'] ?? '-') . "</td>";
                echo "<td>" . $row['id_karyawan'] . "</td>";
                echo "<td>" . $row['id_lokasi'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        exit;
    }
}

echo "<h3>3. 🏗️ Membuat Tabel Patroli</h3>";

$create_table_sql = "
CREATE TABLE `patroli` (
  `id_patroli` int(11) NOT NULL AUTO_INCREMENT,
  `dokumentasi` varchar(255) DEFAULT NULL,
  `tanggal` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `status` varchar(50) NOT NULL,
  `komentar` text DEFAULT NULL,
  `rating` int(5) DEFAULT NULL,
  `id_karyawan` int(11) NOT NULL,
  `id_lokasi` int(11) NOT NULL,
  `id_ceklis` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`id_patroli`),
  KEY `idx_karyawan` (`id_karyawan`),
  KEY `idx_lokasi` (`id_lokasi`),
  KEY `idx_tanggal` (`tanggal`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
";

if ($connection->query($create_table_sql)) {
    echo "<p style='color:green'>✅ Tabel 'patroli' berhasil dibuat</p>";
} else {
    echo "<p style='color:red'>❌ Error membuat tabel: " . $connection->error . "</p>";
    exit;
}

echo "<h3>4. 📋 Struktur Tabel Patroli</h3>";
$structure = $connection->query("DESCRIBE patroli");
if ($structure) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr style='background-color: #f0f0f0;'>";
    echo "<th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th>";
    echo "</tr>";
    while ($field = $structure->fetch_assoc()) {
        echo "<tr>";
        echo "<td><strong>" . $field['Field'] . "</strong></td>";
        echo "<td>" . $field['Type'] . "</td>";
        echo "<td>" . $field['Null'] . "</td>";
        echo "<td>" . $field['Key'] . "</td>";
        echo "<td>" . ($field['Default'] ?? 'NULL') . "</td>";
        echo "<td>" . ($field['Extra'] ?? '') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
}

echo "<h3>5. 📝 Insert Data Sample</h3>";

$sample_data = [
    [
        'dokumentasi' => 'patroli_001.jpg',
        'tanggal' => '2024-01-15 08:00:00',
        'status' => 'Selesai',
        'komentar' => 'Patroli rutin pagi, kondisi aman',
        'rating' => 5,
        'id_karyawan' => 17,
        'id_lokasi' => 8,
        'id_ceklis' => 'CHK001'
    ],
    [
        'dokumentasi' => 'patroli_002.jpg',
        'tanggal' => '2024-01-15 14:30:00',
        'status' => 'Dalam Proses',
        'komentar' => 'Patroli siang, ada beberapa hal yang perlu diperbaiki',
        'rating' => 4,
        'id_karyawan' => 18,
        'id_lokasi' => 7,
        'id_ceklis' => 'CHK002'
    ],
    [
        'dokumentasi' => NULL,
        'tanggal' => '2024-01-15 20:00:00',
        'status' => 'Tertunda',
        'komentar' => 'Patroli malam ditunda karena cuaca buruk',
        'rating' => NULL,
        'id_karyawan' => 17,
        'id_lokasi' => 8,
        'id_ceklis' => 'CHK003'
    ],
    [
        'dokumentasi' => 'patroli_004.mp4',
        'tanggal' => '2024-01-16 06:00:00',
        'status' => 'Selesai',
        'komentar' => 'Patroli subuh, semua normal',
        'rating' => 5,
        'id_karyawan' => 18,
        'id_lokasi' => 7,
        'id_ceklis' => 'CHK004'
    ],
    [
        'dokumentasi' => 'patroli_005.pdf',
        'tanggal' => '2024-01-16 12:00:00',
        'status' => 'Dibatalkan',
        'komentar' => 'Patroli dibatalkan karena maintenance',
        'rating' => NULL,
        'id_karyawan' => 17,
        'id_lokasi' => 8,
        'id_ceklis' => 'CHK005'
    ]
];

$success_count = 0;
foreach ($sample_data as $index => $data) {
    $dokumentasi = $data['dokumentasi'] ? "'{$data['dokumentasi']}'" : "NULL";
    $rating = $data['rating'] ? $data['rating'] : "NULL";
    
    $insert_sql = "INSERT INTO patroli (dokumentasi, tanggal, status, komentar, rating, id_karyawan, id_lokasi, id_ceklis) 
                   VALUES ($dokumentasi, '{$data['tanggal']}', '{$data['status']}', '{$data['komentar']}', $rating, {$data['id_karyawan']}, {$data['id_lokasi']}, '{$data['id_ceklis']}')";
    
    if ($connection->query($insert_sql)) {
        $success_count++;
        echo "<p style='color:green'>✅ Data sample " . ($index + 1) . " berhasil diinsert</p>";
    } else {
        echo "<p style='color:red'>❌ Error insert data " . ($index + 1) . ": " . $connection->error . "</p>";
    }
}

echo "<p><strong>Total data berhasil diinsert: $success_count</strong></p>";

echo "<h3>6. 📊 Verifikasi Data</h3>";
$verify_query = "
SELECT 
    p.id_patroli,
    p.dokumentasi,
    p.tanggal,
    p.status,
    p.komentar,
    p.rating,
    e.employees_name as nama_karyawan,
    b.name as nama_lokasi,
    b.address as alamat_lokasi,
    p.id_ceklis
FROM patroli p
LEFT JOIN employees e ON p.id_karyawan = e.id
LEFT JOIN building b ON p.id_lokasi = b.building_id
ORDER BY p.tanggal DESC
";

$verify_result = $connection->query($verify_query);
if ($verify_result && $verify_result->num_rows > 0) {
    echo "<p style='color:green'>✅ Data patroli berhasil diverifikasi (" . $verify_result->num_rows . " records)</p>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr style='background-color: #f0f0f0;'>";
    echo "<th>ID</th><th>Dokumentasi</th><th>Tanggal</th><th>Status</th><th>Rating</th><th>Karyawan</th><th>Lokasi</th><th>Checklist</th>";
    echo "</tr>";
    
    while ($row = $verify_result->fetch_assoc()) {
        $status_color = '';
        switch($row['status']) {
            case 'Selesai': $status_color = 'background-color: #d4edda;'; break;
            case 'Dalam Proses': $status_color = 'background-color: #fff3cd;'; break;
            case 'Tertunda': $status_color = 'background-color: #cce5ff;'; break;
            case 'Dibatalkan': $status_color = 'background-color: #f8d7da;'; break;
        }
        
        echo "<tr>";
        echo "<td>" . $row['id_patroli'] . "</td>";
        echo "<td>" . ($row['dokumentasi'] ?? '-') . "</td>";
        echo "<td>" . date('d/m/Y H:i', strtotime($row['tanggal'])) . "</td>";
        echo "<td style='$status_color'>" . $row['status'] . "</td>";
        echo "<td>" . ($row['rating'] ? str_repeat('⭐', $row['rating']) : '-') . "</td>";
        echo "<td>" . ($row['nama_karyawan'] ?? 'ID: ' . $row['id_karyawan']) . "</td>";
        echo "<td>" . ($row['nama_lokasi'] ?? 'ID: ' . $row['id_lokasi']) . "</td>";
        echo "<td>" . $row['id_ceklis'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
}

echo "<h3>7. 🎉 Instalasi Selesai</h3>";
echo "<div style='background-color: #d4edda; padding: 15px; border-radius: 5px;'>";
echo "<h4>✅ Tabel Patroli Berhasil Ditambahkan!</h4>";
echo "<ul>";
echo "<li>✅ Tabel 'patroli' dengan struktur lengkap</li>";
echo "<li>✅ $success_count data sample</li>";
echo "<li>✅ Index untuk optimasi query</li>";
echo "<li>✅ Siap digunakan dengan modul admin</li>";
echo "</ul>";
echo "</div>";

echo "<h3>8. 🚀 Akses Modul Patroli</h3>";
echo "<div style='background-color: #cce5ff; padding: 15px; border-radius: 5px;'>";
echo "<p><strong>Login sebagai Operator:</strong></p>";
echo "<ul>";
echo "<li>URL: <a href='sw-admin/login/' target='_blank'>sw-admin/login/</a></li>";
echo "<li>Username: <code>operator</code></li>";
echo "<li>Password: <code>eydcom</code></li>";
echo "</ul>";
echo "<p><strong>Akses Menu Patroli:</strong></p>";
echo "<ul>";
echo "<li>Menu 'Data Patroli' akan tampil di sidebar</li>";
echo "<li>URL: <a href='sw-admin/patroli/' target='_blank'>sw-admin/patroli/</a></li>";
echo "</ul>";
echo "</div>";

$connection->close();
?>
