<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>🏢 Test Data Lokasi dari Database</h2>";

// Include config
require_once 'sw-library/sw-config.php';

echo "<h3>1. Test Koneksi Database</h3>";
if ($connection->connect_error) {
    echo "<p style='color:red'>❌ Database connection failed: " . $connection->connect_error . "</p>";
    exit;
} else {
    echo "<p style='color:green'>✅ Database connected successfully</p>";
    echo "<p><strong>Database:</strong> " . DB_NAME . "</p>";
}

echo "<h3>2. Cek <PERSON>bel Building</h3>";
$table_check = $connection->query("SHOW TABLES LIKE 'building'");
if ($table_check && $table_check->num_rows > 0) {
    echo "<p style='color:green'>✅ Tabel 'building' ditemukan</p>";
} else {
    echo "<p style='color:red'>❌ Tabel 'building' tidak ditemukan</p>";
    exit;
}

echo "<h3>3. Struktur Tabel Building</h3>";
$structure_query = $connection->query("DESCRIBE building");
if ($structure_query) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr style='background-color: #f0f0f0;'>";
    echo "<th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th>";
    echo "</tr>";
    while ($field = $structure_query->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $field['Field'] . "</td>";
        echo "<td>" . $field['Type'] . "</td>";
        echo "<td>" . $field['Null'] . "</td>";
        echo "<td>" . $field['Key'] . "</td>";
        echo "<td>" . ($field['Default'] ?? 'NULL') . "</td>";
        echo "<td>" . ($field['Extra'] ?? '') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color:red'>❌ Gagal mengambil struktur tabel</p>";
}

echo "<h3>4. Data Lokasi yang Tersedia</h3>";
$query = "SELECT building_id, code, name, address, latitude_longtitude, radius FROM building ORDER BY building_id ASC";
$result = $connection->query($query);

if ($result && $result->num_rows > 0) {
    echo "<p style='color:green'>✅ Ditemukan " . $result->num_rows . " lokasi</p>";
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr style='background-color: #f0f0f0;'>";
    echo "<th>ID</th><th>Kode</th><th>Nama Lokasi</th><th>Alamat</th><th>Koordinat</th><th>Radius</th><th>Jumlah Karyawan</th>";
    echo "</tr>";
    
    while ($row = $result->fetch_assoc()) {
        // Hitung jumlah karyawan di lokasi ini
        $emp_query = "SELECT COUNT(*) as total FROM employees WHERE building_id = " . $row['building_id'];
        $emp_result = $connection->query($emp_query);
        $emp_count = $emp_result ? $emp_result->fetch_assoc()['total'] : 0;
        
        echo "<tr>";
        echo "<td>" . $row['building_id'] . "</td>";
        echo "<td>" . $row['code'] . "</td>";
        echo "<td><strong>" . $row['name'] . "</strong></td>";
        echo "<td>" . $row['address'] . "</td>";
        echo "<td>" . $row['latitude_longtitude'] . "</td>";
        echo "<td>" . $row['radius'] . " meter</td>";
        echo "<td><span class='badge' style='background-color: #f39c12; color: white; padding: 3px 8px; border-radius: 3px;'>" . $emp_count . "</span></td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color:red'>❌ Tidak ada data lokasi ditemukan</p>";
}

echo "<h3>5. Test API Endpoint untuk Dropdown</h3>";
echo "<p>Simulasi pemanggilan API untuk dropdown lokasi:</p>";

// Simulasi API call
$api_query = "SELECT building_id, code, name, address FROM building ORDER BY name ASC";
$api_result = $connection->query($api_query);

if ($api_result && $api_result->num_rows > 0) {
    $api_data = array();
    while($row = $api_result->fetch_assoc()) {
        $api_data[] = array(
            'building_id' => $row['building_id'],
            'code' => $row['code'],
            'name' => $row['name'],
            'address' => $row['address'],
            'display_name' => $row['name'] . ' (' . $row['address'] . ')'
        );
    }
    
    echo "<div style='background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>📋 Data JSON untuk Dropdown:</h4>";
    echo "<pre style='background: #ffffff; padding: 10px; border: 1px solid #ddd; border-radius: 3px; overflow-x: auto;'>";
    echo htmlspecialchars(json_encode($api_data, JSON_PRETTY_PRINT));
    echo "</pre>";
    echo "</div>";
    
    echo "<h4>🎯 Preview Dropdown Options:</h4>";
    echo "<select style='width: 100%; padding: 8px; margin: 10px 0;'>";
    echo "<option value=''>-- Pilih Lokasi --</option>";
    foreach($api_data as $item) {
        echo "<option value='" . $item['building_id'] . "'>" . $item['display_name'] . "</option>";
    }
    echo "</select>";
} else {
    echo "<p style='color:red'>❌ Gagal mengambil data untuk API</p>";
}

echo "<h3>6. Test Integrasi dengan Modul Patroli</h3>";
echo "<div style='background-color: #e8f4f8; padding: 15px; border-radius: 5px;'>";
echo "<h4>🔗 URL API Endpoints:</h4>";
echo "<ul>";
echo "<li><strong>Get Lokasi:</strong> <code>sw-admin/sw-mod/patroli/proses.php?action=get_lokasi</code></li>";
echo "<li><strong>Response Format:</strong> JSON Array</li>";
echo "<li><strong>Fields:</strong> building_id, code, name, address, display_name</li>";
echo "</ul>";

echo "<h4>📝 Implementasi di JavaScript:</h4>";
echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 3px;'>";
echo htmlspecialchars("$.ajax({
    url: './sw-mod/patroli/proses.php?action=get_lokasi',
    type: 'GET',
    dataType: 'json',
    success: function(data) {
        var options = '<option value=\"\">-- Pilih Lokasi --</option>';
        $.each(data, function(index, item) {
            options += '<option value=\"' + item.building_id + '\">' + 
                      item.display_name + '</option>';
        });
        $('select[name=\"id_lokasi\"]').html(options);
    }
});");
echo "</pre>";
echo "</div>";

echo "<h3>7. Verifikasi Data Karyawan</h3>";
$emp_query = "SELECT e.id, e.employees_name, e.building_id, b.name as building_name 
              FROM employees e 
              LEFT JOIN building b ON e.building_id = b.building_id 
              ORDER BY e.employees_name ASC";
$emp_result = $connection->query($emp_query);

if ($emp_result && $emp_result->num_rows > 0) {
    echo "<p style='color:green'>✅ Ditemukan " . $emp_result->num_rows . " karyawan</p>";
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr style='background-color: #f0f0f0;'>";
    echo "<th>ID</th><th>Nama Karyawan</th><th>Building ID</th><th>Lokasi</th>";
    echo "</tr>";
    
    $count = 0;
    while ($row = $emp_result->fetch_assoc() && $count < 10) { // Tampilkan 10 data pertama
        echo "<tr>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td>" . $row['employees_name'] . "</td>";
        echo "<td>" . $row['building_id'] . "</td>";
        echo "<td>" . ($row['building_name'] ?? '<em>Tidak ada lokasi</em>') . "</td>";
        echo "</tr>";
        $count++;
    }
    
    if ($emp_result->num_rows > 10) {
        echo "<tr><td colspan='4' style='text-align: center; font-style: italic;'>... dan " . ($emp_result->num_rows - 10) . " karyawan lainnya</td></tr>";
    }
    
    echo "</table>";
} else {
    echo "<p style='color:red'>❌ Tidak ada data karyawan ditemukan</p>";
}

echo "<h3>8. 🎉 Summary</h3>";
echo "<div style='background-color: #d4edda; padding: 15px; border-radius: 5px;'>";
echo "<h4>✅ Hasil Test:</h4>";
echo "<ul>";
echo "<li>✅ Database terkoneksi dengan baik</li>";
echo "<li>✅ Tabel building tersedia dengan struktur yang benar</li>";
echo "<li>✅ Data lokasi tersedia: <strong>" . ($result ? $result->num_rows : 0) . " lokasi</strong></li>";
echo "<li>✅ API endpoint siap untuk dropdown</li>";
echo "<li>✅ Integrasi dengan modul patroli sudah dikonfigurasi</li>";
echo "</ul>";

echo "<h4>🚀 Siap Digunakan:</h4>";
echo "<p>Modul patroli sekarang akan menggunakan data lokasi yang sebenarnya dari database:</p>";
echo "<ul>";
foreach($api_data as $item) {
    echo "<li><strong>" . $item['name'] . "</strong> - " . $item['address'] . " (ID: " . $item['building_id'] . ")</li>";
}
echo "</ul>";
echo "</div>";

$connection->close();
?>
