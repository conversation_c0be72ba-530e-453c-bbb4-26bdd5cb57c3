-- =====================================================
-- TAMBAH TABEL PATROLI KE DATABASE ABSENSI_SIGAP
-- =====================================================
-- Struktur tabel mengikuti format yang sama dengan tabel lainnya
-- =====================================================

USE absensi_sigap;

-- =====================================================
-- BUAT TABEL PATROLI
-- =====================================================

CREATE TABLE `patroli` (
  `id_patroli` int(11) NOT NULL AUTO_INCREMENT,
  `dokumentasi` varchar(255) DEFAULT NULL,
  `tanggal` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `status` varchar(50) NOT NULL,
  `komentar` text DEFAULT NULL,
  `rating` int(5) DEFAULT NULL,
  `id_karyawan` int(11) NOT NULL,
  `id_lokasi` int(11) NOT NULL,
  `id_ceklis` varchar(50) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- =====================================================
-- TAMBAH PRIMARY KEY DAN INDEX
-- =====================================================

ALTER TABLE `patroli`
  ADD PRIMARY KEY (`id_patroli`),
  ADD KEY `idx_karyawan` (`id_karyawan`),
  ADD KEY `idx_lokasi` (`id_lokasi`),
  ADD KEY `idx_tanggal` (`tanggal`),
  ADD KEY `idx_status` (`status`);

-- =====================================================
-- SET AUTO_INCREMENT
-- =====================================================

ALTER TABLE `patroli`
  MODIFY `id_patroli` int(11) NOT NULL AUTO_INCREMENT;

-- =====================================================
-- TAMBAH FOREIGN KEY CONSTRAINTS (OPSIONAL)
-- =====================================================

-- Uncomment jika ingin menambahkan foreign key constraints
-- ALTER TABLE `patroli`
--   ADD CONSTRAINT `fk_patroli_karyawan` FOREIGN KEY (`id_karyawan`) REFERENCES `employees` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
--   ADD CONSTRAINT `fk_patroli_lokasi` FOREIGN KEY (`id_lokasi`) REFERENCES `building` (`building_id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- =====================================================
-- INSERT DATA SAMPLE
-- =====================================================

INSERT INTO `patroli` (`dokumentasi`, `tanggal`, `status`, `komentar`, `rating`, `id_karyawan`, `id_lokasi`, `id_ceklis`) VALUES
('patroli_001.jpg', '2024-01-15 08:00:00', 'Selesai', 'Patroli rutin pagi, kondisi aman', 5, 17, 8, 'CHK001'),
('patroli_002.jpg', '2024-01-15 14:30:00', 'Dalam Proses', 'Patroli siang, ada beberapa hal yang perlu diperbaiki', 4, 18, 7, 'CHK002'),
(NULL, '2024-01-15 20:00:00', 'Tertunda', 'Patroli malam ditunda karena cuaca buruk', NULL, 17, 8, 'CHK003'),
('patroli_004.mp4', '2024-01-16 06:00:00', 'Selesai', 'Patroli subuh, semua normal', 5, 18, 7, 'CHK004'),
('patroli_005.pdf', '2024-01-16 12:00:00', 'Dibatalkan', 'Patroli dibatalkan karena maintenance', NULL, 17, 8, 'CHK005');

-- =====================================================
-- VERIFIKASI TABEL BERHASIL DIBUAT
-- =====================================================

SELECT 'Tabel patroli berhasil dibuat!' as status;

-- Tampilkan struktur tabel
DESCRIBE patroli;

-- Tampilkan data yang berhasil diinsert
SELECT * FROM patroli ORDER BY tanggal DESC;

-- Hitung jumlah data
SELECT COUNT(*) as total_data FROM patroli;

-- =====================================================
-- QUERY UNTUK TESTING DENGAN JOIN
-- =====================================================

-- Query untuk menampilkan data patroli dengan nama karyawan dan lokasi
SELECT 
    p.id_patroli,
    p.dokumentasi,
    p.tanggal,
    p.status,
    p.komentar,
    p.rating,
    e.employees_name as nama_karyawan,
    b.name as nama_lokasi,
    b.address as alamat_lokasi,
    p.id_ceklis
FROM patroli p
LEFT JOIN employees e ON p.id_karyawan = e.id
LEFT JOIN building b ON p.id_lokasi = b.building_id
ORDER BY p.tanggal DESC;

-- =====================================================
-- SELESAI
-- =====================================================

SELECT 'Instalasi tabel patroli selesai!' as message;
