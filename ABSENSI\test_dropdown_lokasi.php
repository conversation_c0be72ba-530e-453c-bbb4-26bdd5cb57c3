<!DOCTYPE html>
<html>
<head>
    <title>Test Dropdown Lokasi</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/js/bootstrap.min.js"></script>
</head>
<body>
<div class="container" style="margin-top: 20px;">
    <h2>🏢 Test Dropdown Lokasi - Modul Patroli</h2>
    
    <div class="row">
        <div class="col-md-8">
            <div class="panel panel-primary">
                <div class="panel-heading">
                    <h3 class="panel-title">Test Form Patroli dengan Dropdown Lokasi</h3>
                </div>
                <div class="panel-body">
                    <form id="test-form">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Karyawan <span class="text-danger">*</span></label>
                                    <select name="id_karyawan" id="dropdown-karyawan" class="form-control" required>
                                        <option value="">-- Loading karyawan... --</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Lokasi <span class="text-danger">*</span></label>
                                    <select name="id_lokasi" id="dropdown-lokasi" class="form-control" required>
                                        <option value="">-- Loading lokasi... --</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Tanggal Patroli <span class="text-danger">*</span></label>
                                    <input type="datetime-local" name="tanggal" class="form-control" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Status <span class="text-danger">*</span></label>
                                    <select name="status" class="form-control" required>
                                        <option value="">-- Pilih Status --</option>
                                        <option value="Selesai">Selesai</option>
                                        <option value="Dalam Proses">Dalam Proses</option>
                                        <option value="Tertunda">Tertunda</option>
                                        <option value="Dibatalkan">Dibatalkan</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label>Komentar</label>
                            <textarea name="komentar" class="form-control" rows="3" placeholder="Komentar patroli..."></textarea>
                        </div>
                        
                        <button type="button" id="test-submit" class="btn btn-primary">
                            <i class="glyphicon glyphicon-check"></i> Test Submit
                        </button>
                        <button type="button" id="reload-dropdown" class="btn btn-info">
                            <i class="glyphicon glyphicon-refresh"></i> Reload Dropdown
                        </button>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="panel panel-info">
                <div class="panel-heading">
                    <h3 class="panel-title">Debug Info</h3>
                </div>
                <div class="panel-body">
                    <div id="debug-info">
                        <p><strong>Status:</strong> <span id="status">Initializing...</span></p>
                        <p><strong>Lokasi Loaded:</strong> <span id="lokasi-count">0</span></p>
                        <p><strong>Karyawan Loaded:</strong> <span id="karyawan-count">0</span></p>
                        <hr>
                        <div id="selected-info"></div>
                    </div>
                </div>
            </div>
            
            <div class="panel panel-success">
                <div class="panel-heading">
                    <h3 class="panel-title">Data Lokasi dari Database</h3>
                </div>
                <div class="panel-body">
                    <div id="lokasi-data">Loading...</div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-12">
            <div class="panel panel-warning">
                <div class="panel-heading">
                    <h3 class="panel-title">Response Log</h3>
                </div>
                <div class="panel-body">
                    <div id="response-log" style="height: 200px; overflow-y: auto; background: #f5f5f5; padding: 10px; font-family: monospace; font-size: 12px;">
                        <div class="log-entry">Initializing test page...</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Log function
    function addLog(message, type = 'info') {
        var timestamp = new Date().toLocaleTimeString();
        var logClass = type === 'error' ? 'text-danger' : (type === 'success' ? 'text-success' : 'text-info');
        $('#response-log').append('<div class="log-entry ' + logClass + '">[' + timestamp + '] ' + message + '</div>');
        $('#response-log').scrollTop($('#response-log')[0].scrollHeight);
    }
    
    // Load dropdown data
    function loadDropdowns() {
        $('#status').text('Loading dropdowns...');
        addLog('Starting to load dropdown data...');
        
        // Load Karyawan
        $.ajax({
            url: 'sw-admin/sw-mod/patroli/proses.php?action=get_karyawan',
            type: 'GET',
            dataType: 'json',
            success: function(data) {
                addLog('Karyawan data loaded: ' + data.length + ' items', 'success');
                var options = '<option value="">-- Pilih Karyawan --</option>';
                $.each(data, function(index, item) {
                    options += '<option value="' + item.id + '">' + item.employees_code + ' - ' + item.employees_name + '</option>';
                });
                $('#dropdown-karyawan').html(options);
                $('#karyawan-count').text(data.length);
            },
            error: function(xhr, status, error) {
                addLog('Error loading karyawan: ' + error, 'error');
                $('#dropdown-karyawan').html('<option value="">Error loading karyawan</option>');
            }
        });
        
        // Load Lokasi
        $.ajax({
            url: 'sw-admin/sw-mod/patroli/proses.php?action=get_lokasi',
            type: 'GET',
            dataType: 'json',
            success: function(data) {
                addLog('Lokasi data loaded: ' + data.length + ' items', 'success');
                addLog('Lokasi data: ' + JSON.stringify(data), 'info');
                
                var options = '<option value="">-- Pilih Lokasi --</option>';
                var lokasiInfo = '<ul>';
                
                $.each(data, function(index, item) {
                    options += '<option value="' + item.building_id + '" data-code="' + item.code + '" data-address="' + item.address + '">' + 
                              item.display_name + '</option>';
                    lokasiInfo += '<li><strong>' + item.name + '</strong><br><small>' + item.address + '</small><br><em>ID: ' + item.building_id + '</em></li>';
                });
                
                lokasiInfo += '</ul>';
                $('#dropdown-lokasi').html(options);
                $('#lokasi-count').text(data.length);
                $('#lokasi-data').html(lokasiInfo);
                $('#status').text('Ready');
            },
            error: function(xhr, status, error) {
                addLog('Error loading lokasi: ' + error, 'error');
                addLog('XHR Response: ' + xhr.responseText, 'error');
                $('#dropdown-lokasi').html('<option value="">Error loading lokasi</option>');
                $('#lokasi-data').html('<p class="text-danger">Error loading data</p>');
            }
        });
    }
    
    // Handle dropdown changes
    $('#dropdown-lokasi').change(function() {
        var selectedOption = $(this).find('option:selected');
        var info = '';
        if ($(this).val()) {
            info = '<strong>Selected Lokasi:</strong><br>' +
                   'ID: ' + $(this).val() + '<br>' +
                   'Code: ' + selectedOption.data('code') + '<br>' +
                   'Address: ' + selectedOption.data('address') + '<br>' +
                   'Display: ' + selectedOption.text();
            addLog('Lokasi selected: ' + selectedOption.text(), 'success');
        }
        $('#selected-info').html(info);
    });
    
    $('#dropdown-karyawan').change(function() {
        if ($(this).val()) {
            addLog('Karyawan selected: ' + $(this).find('option:selected').text(), 'success');
        }
    });
    
    // Test submit
    $('#test-submit').click(function() {
        var formData = {
            id_karyawan: $('#dropdown-karyawan').val(),
            id_lokasi: $('#dropdown-lokasi').val(),
            tanggal: $('input[name="tanggal"]').val(),
            status: $('select[name="status"]').val(),
            komentar: $('textarea[name="komentar"]').val()
        };
        
        addLog('Test submit with data: ' + JSON.stringify(formData), 'info');
        
        if (!formData.id_karyawan || !formData.id_lokasi || !formData.tanggal || !formData.status) {
            addLog('Validation failed: Missing required fields', 'error');
            alert('Please fill all required fields');
            return;
        }
        
        addLog('Validation passed - ready for actual submission', 'success');
        alert('Test successful! Data is ready for submission.');
    });
    
    // Reload dropdown
    $('#reload-dropdown').click(function() {
        addLog('Reloading dropdowns...', 'info');
        loadDropdowns();
    });
    
    // Initialize
    loadDropdowns();
    
    // Set default datetime
    var now = new Date();
    now.setMinutes(now.getMinutes() - now.getTimezoneOffset());
    $('input[name="tanggal"]').val(now.toISOString().slice(0, 16));
});
</script>

</body>
</html>
