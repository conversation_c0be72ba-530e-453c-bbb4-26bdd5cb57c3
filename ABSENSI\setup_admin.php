<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>🔧 Setup Admin Users</h2>";

// Include config
require_once 'sw-library/sw-config.php';

// Check database connection
if ($connection->connect_error) {
    echo "<p style='color:red'>❌ Database connection failed: " . $connection->connect_error . "</p>";
    exit;
} else {
    echo "<p style='color:green'>✅ Database connected successfully</p>";
}

echo "<h3>1. Creating Admin Users</h3>";

// Salt yang digunakan sistem
$salt = '$%DSuTyr47542@#&*!=QxR094{a911}+';
$password_plain = 'eydcom';
$password_hash = hash('sha256', $salt . $password_plain);

echo "<p><strong>Password Info:</strong></p>";
echo "<p>Plain Password: <code>" . $password_plain . "</code></p>";
echo "<p>Salt: <code>" . $salt . "</code></p>";
echo "<p>Hash: <code>" . $password_hash . "</code></p>";

// Hapus user yang sudah ada
echo "<h4>🗑️ Cleaning existing users...</h4>";
$delete_query = "DELETE FROM user WHERE username IN ('eydcom.com', 'operator')";
if ($connection->query($delete_query)) {
    echo "<p style='color:green'>✅ Existing users cleaned</p>";
} else {
    echo "<p style='color:orange'>⚠️ No existing users to clean or error: " . $connection->error . "</p>";
}

// Insert Administrator
echo "<h4>👑 Creating Administrator...</h4>";
$admin_query = "INSERT INTO user (
    user_id,
    username, 
    email, 
    password, 
    fullname, 
    registered, 
    created_login, 
    last_login, 
    session, 
    ip, 
    browser, 
    level
) VALUES (
    1,
    'eydcom.com',
    '<EMAIL>',
    '$password_hash',
    'EYD COMPUTER',
    '2021-02-03 10:22:00',
    NOW(),
    NOW(),
    '-',
    '127.0.0.1',
    'Google Chrome',
    1
)";

if ($connection->query($admin_query)) {
    echo "<p style='color:green'>✅ Administrator created successfully</p>";
} else {
    echo "<p style='color:red'>❌ Error creating administrator: " . $connection->error . "</p>";
}

// Insert Operator
echo "<h4>👤 Creating Operator...</h4>";
$operator_query = "INSERT INTO user (
    user_id,
    username, 
    email, 
    password, 
    fullname, 
    registered, 
    created_login, 
    last_login, 
    session, 
    ip, 
    browser, 
    level
) VALUES (
    3,
    'operator',
    '<EMAIL>',
    '$password_hash',
    'Operator',
    '2023-09-15 12:14:44',
    NOW(),
    NOW(),
    '-',
    '127.0.0.1',
    'Google Chrome',
    2
)";

if ($connection->query($operator_query)) {
    echo "<p style='color:green'>✅ Operator created successfully</p>";
} else {
    echo "<p style='color:red'>❌ Error creating operator: " . $connection->error . "</p>";
}

// Pastikan user_level table ada
echo "<h4>📋 Setting up user levels...</h4>";
$level_query = "INSERT IGNORE INTO user_level (level_id, level_name) VALUES 
(1, 'Administrator'),
(2, 'Operator')";

if ($connection->query($level_query)) {
    echo "<p style='color:green'>✅ User levels setup complete</p>";
} else {
    echo "<p style='color:red'>❌ Error setting up user levels: " . $connection->error . "</p>";
}

// Verifikasi hasil
echo "<h3>2. 📊 Verification Results</h3>";
$verify_query = "SELECT 
    user_id,
    username,
    email,
    fullname,
    level,
    registered,
    CASE 
        WHEN level = 1 THEN 'Administrator (Akses Penuh)'
        WHEN level = 2 THEN 'Operator (Akses Terbatas)'
        ELSE 'Unknown'
    END as level_description
FROM user 
WHERE username IN ('eydcom.com', 'operator')
ORDER BY user_id";

$result = $connection->query($verify_query);

if ($result && $result->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr style='background-color: #f0f0f0;'>";
    echo "<th>ID</th><th>Username</th><th>Email</th><th>Fullname</th><th>Level</th><th>Description</th>";
    echo "</tr>";
    
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['user_id'] . "</td>";
        echo "<td><strong>" . $row['username'] . "</strong></td>";
        echo "<td>" . $row['email'] . "</td>";
        echo "<td>" . $row['fullname'] . "</td>";
        echo "<td>" . $row['level'] . "</td>";
        echo "<td>" . $row['level_description'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color:red'>❌ No users found after creation</p>";
}

// Test login
echo "<h3>3. 🔐 Login Test</h3>";
$test_users = [
    ['username' => 'eydcom.com', 'password' => 'eydcom'],
    ['username' => 'operator', 'password' => 'eydcom']
];

foreach ($test_users as $test_user) {
    $test_username = $test_user['username'];
    $test_password = $test_user['password'];
    $test_hash = hash('sha256', $salt . $test_password);
    
    $login_test_query = "SELECT * FROM user WHERE username='$test_username' AND password='$test_hash'";
    $login_result = $connection->query($login_test_query);
    
    if ($login_result && $login_result->num_rows > 0) {
        $user_data = $login_result->fetch_assoc();
        echo "<p style='color:green'>✅ <strong>$test_username</strong> - Login test PASSED</p>";
        echo "<p style='margin-left: 20px;'>→ Password: <code>$test_password</code></p>";
        echo "<p style='margin-left: 20px;'>→ Level: " . $user_data['level'] . " (" . $user_data['fullname'] . ")</p>";
    } else {
        echo "<p style='color:red'>❌ <strong>$test_username</strong> - Login test FAILED</p>";
    }
}

echo "<h3>4. 🚀 Ready to Login</h3>";
echo "<div style='background-color: #e8f5e8; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<p><strong>🔗 Admin Login URL:</strong></p>";
echo "<p><a href='sw-admin/login/' target='_blank'>http://localhost/SC%20ABSENSI%20SIGAP/ABSENSI/sw-admin/login/</a></p>";
echo "<br>";
echo "<p><strong>👑 Administrator Login:</strong></p>";
echo "<p>Username: <code>eydcom.com</code></p>";
echo "<p>Password: <code>eydcom</code></p>";
echo "<br>";
echo "<p><strong>👤 Operator Login:</strong></p>";
echo "<p>Username: <code>operator</code></p>";
echo "<p>Password: <code>eydcom</code></p>";
echo "</div>";

$connection->close();
?>
