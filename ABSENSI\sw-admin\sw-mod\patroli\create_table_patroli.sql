-- =====================================================
-- SQL untuk membuat tabel patroli
-- =====================================================

CREATE TABLE `patroli` (
  `id_patroli` int(11) NOT NULL AUTO_INCREMENT,
  `dokumentasi` varchar(255) DEFAULT NULL COMMENT 'Nama file dokumentasi (foto/video/pdf)',
  `tanggal` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Tanggal dan waktu patroli',
  `status` enum('Selesai','Dalam Proses','Tertunda','Dibatalkan') NOT NULL COMMENT 'Status patroli',
  `komentar` text DEFAULT NULL COMMENT 'Komentar atau catatan tambahan',
  `rating` tinyint(1) DEFAULT NULL COMMENT 'Rating 1-5 bintang',
  `id_karyawan` int(11) NOT NULL COMMENT 'ID karyawan yang melakukan patroli',
  `id_lokasi` int(11) NOT NULL COMMENT 'ID lokasi patroli',
  `id_ceklis` varchar(50) DEFAULT NULL COMMENT 'ID checklist terkait',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id_patroli`),
  KEY `idx_karyawan` (`id_karyawan`),
  KEY `idx_lokasi` (`id_lokasi`),
  KEY `idx_tanggal` (`tanggal`),
  KEY `idx_status` (`status`),
  CONSTRAINT `fk_patroli_karyawan` FOREIGN KEY (`id_karyawan`) REFERENCES `employees` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_patroli_lokasi` FOREIGN KEY (`id_lokasi`) REFERENCES `building` (`building_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='Tabel untuk menyimpan data patroli';

-- =====================================================
-- Contoh data dummy untuk testing (opsional)
-- =====================================================

-- Contoh data dummy dengan lokasi sebenarnya dari database
-- INSERT INTO `patroli` (
--   `id_karyawan`,
--   `id_lokasi`,
--   `tanggal`,
--   `status`,
--   `rating`,
--   `id_ceklis`,
--   `komentar`,
--   `dokumentasi`
-- ) VALUES
-- (17, 8, '2024-01-15 08:00:00', 'Selesai', 5, 'CHK001', 'Patroli rutin pagi di EYD COMPUTER KB4 - KEBON EMPAT', 'patroli_20240115_001.jpg'),
-- (18, 7, '2024-01-15 14:30:00', 'Dalam Proses', 4, 'CHK002', 'Patroli siang di EYD COMPUTER KL7 - KELAPA TUJUH', NULL),
-- (17, 8, '2024-01-15 20:00:00', 'Tertunda', NULL, 'CHK003', 'Patroli malam di EYD COMPUTER KB4 ditunda karena cuaca buruk', NULL);

-- Lokasi yang tersedia di database:
-- ID 7: EYD COMPUTER KL7 (KELAPA TUJUH)
-- ID 8: EYD COMPUTER KB4 (KEBON EMPAT)

-- =====================================================
-- Index tambahan untuk optimasi query
-- =====================================================

-- CREATE INDEX idx_patroli_tanggal_status ON patroli(tanggal, status);
-- CREATE INDEX idx_patroli_karyawan_tanggal ON patroli(id_karyawan, tanggal);
-- CREATE INDEX idx_patroli_lokasi_tanggal ON patroli(id_lokasi, tanggal);
