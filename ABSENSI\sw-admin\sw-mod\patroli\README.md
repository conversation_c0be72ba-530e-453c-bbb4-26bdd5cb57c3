# Modul Patroli - Sistem Absensi

## 📋 Deskripsi
Modul Patroli adalah fitur untuk mengelola data patroli keamanan yang dilakukan oleh karyawan di berbagai lokasi. Modul ini dapat diakses oleh admin dan operator.

## 🗂️ Struktur Tabel Patroli

| Field | Type | Description |
|-------|------|-------------|
| `id_patroli` | int(11) AUTO_INCREMENT | Primary key |
| `dokumentasi` | varchar(255) NULL | Nama file dokumentasi (foto/video/pdf) |
| `tanggal` | timestamp | Tanggal dan waktu patroli |
| `status` | enum | Status: Selesai, Dalam Pro<PERSON>, Tertunda, Dibatalkan |
| `komentar` | text NULL | Komentar atau catatan tambahan |
| `rating` | tinyint(1) NULL | Rating 1-5 bintang |
| `id_karyawan` | int(11) | ID karyawan (FK ke employees.id) |
| `id_lokasi` | int(11) | ID lokasi (FK ke building.building_id) |
| `id_ceklis` | varchar(50) NULL | ID checklist terkait |

## 📁 Struktur File

```
sw-admin/sw-mod/patroli/
├── patroli.php          # Halaman utama modul
├── proses.php           # Handler CRUD operations
├── sw-datatable.php     # Server-side DataTable
├── scripts.js           # JavaScript functions
├── create_table_patroli.sql  # SQL untuk membuat tabel
└── README.md            # Dokumentasi ini
```

## 🔧 Fitur

### ✅ Fitur yang Tersedia:
1. **Tambah Data Patroli**
   - Form input dengan validasi
   - Upload dokumentasi (foto/video/PDF)
   - Dropdown karyawan dan lokasi
   - Rating 1-5 bintang
   - Status patroli

2. **Lihat Data Patroli**
   - DataTable dengan server-side processing
   - Pencarian dan sorting
   - Pagination
   - Filter berdasarkan kolom

3. **Edit Data Patroli**
   - Modal edit dengan data pre-filled
   - Update dokumentasi (opsional)
   - Validasi form

4. **Hapus Data Patroli**
   - Konfirmasi sebelum hapus
   - Auto-delete file dokumentasi

5. **View Dokumentasi**
   - Preview gambar dalam modal
   - Download file PDF/video

### 📊 Status Patroli:
- **Selesai** (hijau) - Patroli telah selesai dilakukan
- **Dalam Proses** (kuning) - Patroli sedang berlangsung
- **Tertunda** (biru) - Patroli ditunda sementara
- **Dibatalkan** (merah) - Patroli dibatalkan

### ⭐ Rating System:
- 1 ⭐ - Buruk
- 2 ⭐⭐ - Kurang
- 3 ⭐⭐⭐ - Cukup
- 4 ⭐⭐⭐⭐ - Baik
- 5 ⭐⭐⭐⭐⭐ - Sangat Baik

## 🔐 Akses Level

| Level | Akses |
|-------|-------|
| Administrator (Level 1) | **TIDAK ADA AKSES** - Menu tidak tampil |
| Operator (Level 2) | **FULL ACCESS** - CRUD semua data patroli |

### 🚫 Pembatasan Akses:
- **Administrator** tidak dapat melihat menu "Data Patroli"
- **Administrator** tidak dapat mengakses halaman patroli secara langsung
- **Hanya Operator** yang memiliki akses penuh ke modul patroli
- Jika Administrator mencoba mengakses paksa, akan muncul pesan "Akses Ditolak"

## 📂 Upload Dokumentasi

### Format yang Didukung:
- **Gambar**: JPG, JPEG, PNG, GIF
- **Video**: MP4, AVI, MOV
- **Dokumen**: PDF

### Batasan:
- Ukuran maksimal: 10MB
- File disimpan di: `sw-content/patroli/`
- Nama file: `patroli_timestamp_random.ext`

## 🚀 Instalasi

### 1. Buat Tabel Database
```sql
-- Jalankan script dari create_table_patroli.sql
CREATE TABLE patroli (...);
```

### 2. Buat Direktori Upload
```bash
mkdir sw-content/patroli/
chmod 755 sw-content/patroli/
```

### 3. Akses Menu
- Login sebagai admin/operator
- Menu "Data Patroli" akan muncul di sidebar

## 🔧 Konfigurasi

### Dependencies:
- jQuery
- DataTables
- Bootstrap 3
- SweetAlert
- Font Awesome

### File yang Dibutuhkan:
- Tabel `employees` (untuk dropdown karyawan)
- Tabel `building` (untuk dropdown lokasi - sudah terintegrasi)
- Session management aktif

### Integrasi Database:
- **Tabel Building:** Menggunakan data lokasi yang sebenarnya dari `building` table
- **Fields Building:** `building_id`, `code`, `name`, `address`, `latitude_longtitude`, `radius`
- **Lokasi Tersedia:**
  - ID 7: EYD COMPUTER KL7 (KELAPA TUJUH)
  - ID 8: EYD COMPUTER KB4 (KEBON EMPAT)
- **Foreign Key:** `id_lokasi` → `building.building_id`

## 📱 Responsive Design
- Mobile-friendly interface
- Bootstrap responsive grid
- Touch-friendly buttons

## 🔍 Troubleshooting

### Error Upload File:
1. Cek permission direktori `sw-content/patroli/`
2. Pastikan ukuran file < 10MB
3. Cek format file yang didukung

### DataTable Tidak Muncul:
1. Cek koneksi database
2. Pastikan tabel `employees` dan `building` ada
3. Cek JavaScript console untuk error

### Menu Tidak Muncul:
1. Pastikan user sudah login
2. Cek level user (harus level 1 atau 2)
3. Clear browser cache

## 📞 Support
Untuk bantuan teknis, hubungi administrator sistem.
